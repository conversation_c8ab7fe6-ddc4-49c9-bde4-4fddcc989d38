package mysql

import (
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"payment-backend/internal/db"
	"payment-backend/internal/domain"
)

// orderRepository MySQL订单仓储实现
type orderRepository struct {
	db *gorm.DB
}

// NewOrderRepository 创建MySQL订单仓储
func NewOrderRepository(database *gorm.DB) domain.OrderRepository {
	return &orderRepository{
		db: database,
	}
}

// Create 创建订单记录
func (r *orderRepository) Create(order *domain.Order) error {
	model := &db.OrderModel{}
	model.FromDomain(order)

	if err := r.db.Create(model).Error; err != nil {
		return fmt.Errorf("failed to create order: %w", err)
	}

	// 更新domain对象的ID
	order.ID = model.ID
	order.OrderID = model.OrderID
	order.CreatedAt = model.CreatedAt
	order.UpdatedAt = model.UpdatedAt

	return nil
}

// GetByID 根据ID获取订单记录
func (r *orderRepository) GetByID(id uint64) (*domain.Order, error) {
	var model db.OrderModel

	if err := r.db.Where("id = ? AND deleted = 0", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order with ID %d not found", id)
		}
		return nil, fmt.Errorf("failed to get order by ID: %w", err)
	}

	return model.ToDomain(), nil
}

// GetByOrderID 根据订单ID获取订单记录
func (r *orderRepository) GetByOrderID(orderID string) (*domain.Order, error) {
	var model db.OrderModel

	if err := r.db.Where("order_id = ? AND deleted = 0", orderID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order with order ID %s not found", orderID)
		}
		return nil, fmt.Errorf("failed to get order by order ID: %w", err)
	}

	return model.ToDomain(), nil
}

// GetByOrderIDForUpdate 根据订单ID获取订单记录，并加排他锁（FOR UPDATE）
// 只能在事务上下文中使用，否则会 panic。
func (r *orderRepository) GetByOrderIDForUpdate(tx any, orderID string) (*domain.Order, error) {

	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return nil, fmt.Errorf("invalid transaction type")
	}
	// 如果 tx 为 nil，就默认使用 r.db，这可能会导致不在事务中调用 FOR UPDATE，危险
	if gormTx == nil {
		return nil, fmt.Errorf("GetByOrderIDForUpdate must be called within a transaction")
	}

	var model db.OrderModel
	if err := gormTx.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("order_id = ? AND deleted = 0", orderID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order with order ID %s not found", orderID)
		}
		return nil, fmt.Errorf("failed to get order by order ID (FOR UPDATE): %w", err)
	}

	return model.ToDomain(), nil
}

// GetByUserID 根据用户ID获取订单列表
func (r *orderRepository) GetByUserID(userID string, filter *domain.OrderFilter, pagination *domain.PaginationRequest) ([]*domain.Order, int64, error) {
	filter.UserID = &userID
	return r.ListWithFilters(filter, pagination)
}

// GetByPSPPaymentID 根据PSP支付ID获取订单记录
func (r *orderRepository) GetByPSPPaymentID(pspPaymentID string) (*domain.Order, error) {
	var model db.OrderModel

	if err := r.db.Where("psp_payment_id = ? AND deleted = 0", pspPaymentID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order with PSP payment ID %s not found", pspPaymentID)
		}
		return nil, fmt.Errorf("failed to get order by PSP payment ID: %w", err)
	}

	return model.ToDomain(), nil
}

// Update 更新订单记录
func (r *orderRepository) Update(order *domain.Order) error {
	model := &db.OrderModel{}
	model.FromDomain(order)

	// 使用Select来指定要更新的字段，避免零值问题
	result := r.db.Model(&db.OrderModel{}).
		Where("id = ? AND deleted = 0", order.ID).
		Select("user_id", "product_id", "product_desc", "product_snapshot", "price_id", "quantity",
			"amount", "net_amount", "currency", "pay_status", "pay_ret", "payed_method", "psp_provider",
			"card_number", "payed_at", "refund_status", "refunded_at",
			"psp_product_id", "psp_product_desc", "psp_price_id",
			"psp_payment_id", "psp_customer_id", "psp_customer_email",
			"psp_customer_name", "psp_customer_country", "psp_subscription_id",
			"updated_at").
		Updates(model)

	if result.Error != nil {
		return fmt.Errorf("failed to update order: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with ID %d not found or already deleted", order.ID)
	}

	return nil
}

// UpdateStatus 更新订单支付状态
func (r *orderRepository) UpdateStatus(orderID string, payStatus string) error {
	result := r.db.Model(&db.OrderModel{}).
		Where("order_id = ? AND deleted = 0", orderID).
		Updates(map[string]interface{}{
			"pay_status": payStatus,
			"updated_at": gorm.Expr("NOW()"),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to update order status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}

// SoftDelete 软删除订单
func (r *orderRepository) SoftDelete(orderID string) error {
	result := r.db.Model(&db.OrderModel{}).
		Where("order_id = ? AND deleted = 0", orderID).
		Updates(map[string]interface{}{
			"deleted":    1,
			"deleted_at": gorm.Expr("NOW()"),
			"updated_at": gorm.Expr("NOW()"),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to soft delete order: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}

// List 获取订单列表
func (r *orderRepository) List(limit, offset int) ([]*domain.Order, error) {
	var models []db.OrderModel

	query := r.db.Where("deleted = 0").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset)

	if err := query.Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to list orders: %w", err)
	}

	// 转换为domain模型
	orders := make([]*domain.Order, len(models))
	for i, model := range models {
		orders[i] = model.ToDomain()
	}

	return orders, nil
}

// Count 统计订单总数
func (r *orderRepository) Count() (int64, error) {
	var count int64

	if err := r.db.Model(&db.OrderModel{}).Where("deleted = 0").Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count orders: %w", err)
	}

	return count, nil
}

// ListWithFilters 根据过滤条件获取订单列表
func (r *orderRepository) ListWithFilters(filter *domain.OrderFilter, pagination *domain.PaginationRequest) ([]*domain.Order, int64, error) {
	var models []db.OrderModel
	var total int64

	// 构建查询条件
	query := r.db.Model(&db.OrderModel{}).Where("deleted = 0")

	// 应用过滤条件
	if filter != nil {
		if filter.UserID != nil && *filter.UserID != "" {
			query = query.Where("user_id = ?", *filter.UserID)
		}
		if filter.Currency != nil && *filter.Currency != "" {
			query = query.Where("currency = ?", *filter.Currency)
		}
		if filter.PayStatus != nil && *filter.PayStatus != "" {
			query = query.Where("pay_status = ?", *filter.PayStatus)
		}
		if filter.PayedMethod != nil && *filter.PayedMethod != "" {
			query = query.Where("payed_method = ?", *filter.PayedMethod)
		}
		if filter.PSPProvider != nil && *filter.PSPProvider != "" {
			query = query.Where("psp_provider = ?", *filter.PSPProvider)
		}
		if filter.PayedAtStart != nil {
			query = query.Where("payed_at >= ?", *filter.PayedAtStart)
		}
		if filter.PayedAtEnd != nil {
			query = query.Where("payed_at <= ?", *filter.PayedAtEnd)
		}
		if filter.RefundStatus != nil && *filter.RefundStatus != "" {
			query = query.Where("refund_status = ?", *filter.RefundStatus)
		}
		if filter.RefundedAtStart != nil {
			query = query.Where("refunded_at >= ?", *filter.RefundedAtStart)
		}
		if filter.RefundedAtEnd != nil {
			query = query.Where("refunded_at <= ?", *filter.RefundedAtEnd)
		}
		if filter.PSPPriceID != nil && *filter.PSPPriceID != "" {
			query = query.Where("psp_price_id = ?", *filter.PSPPriceID)
		}
		if filter.PSPCustomerEmail != nil && *filter.PSPCustomerEmail != "" {
			query = query.Where("psp_customer_email = ?", *filter.PSPCustomerEmail)
		}
		if filter.PSPSubscriptionID != nil && *filter.PSPSubscriptionID != "" {
			query = query.Where("psp_subscription_id = ?", *filter.PSPSubscriptionID)
		}
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count filtered orders: %w", err)
	}

	// 应用分页和排序
	if pagination != nil {
		query = query.Order("created_at DESC").
			Limit(pagination.Limit).
			Offset(pagination.Offset)
	} else {
		query = query.Order("created_at DESC")
	}

	// 执行查询
	if err := query.Find(&models).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list filtered orders: %w", err)
	}

	// 转换为domain模型
	orders := make([]*domain.Order, len(models))
	for i, model := range models {
		orders[i] = model.ToDomain()
	}

	return orders, total, nil
}

// CountByUserID 统计用户的订单数量
func (r *orderRepository) CountByUserID(userID string) (int64, error) {
	var count int64

	if err := r.db.Model(&db.OrderModel{}).
		Where("user_id = ? AND deleted = 0", userID).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count orders by user ID: %w", err)
	}

	return count, nil
}

// CreateWithTransaction 在事务中创建订单记录
func (r *orderRepository) CreateWithTransaction(tx any, order *domain.Order) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	model := &db.OrderModel{}
	model.FromDomain(order)

	if err := gormTx.Create(model).Error; err != nil {
		return fmt.Errorf("failed to create order in transaction: %w", err)
	}

	// 更新domain对象的ID
	order.ID = model.ID
	order.OrderID = model.OrderID
	order.CreatedAt = model.CreatedAt
	order.UpdatedAt = model.UpdatedAt

	return nil
}

// UpdateWithTransaction 在事务中更新订单记录
func (r *orderRepository) UpdateWithTransaction(tx any, order *domain.Order) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	model := &db.OrderModel{}
	model.FromDomain(order)

	result := gormTx.Model(&db.OrderModel{}).
		Where("id = ? AND deleted = 0", order.ID).
		Select("user_id", "product_id", "product_desc", "product_snapshot", "price_id", "quantity",
			"amount", "net_amount", "currency", "pay_status", "pay_ret", "payed_method", "psp_provider",
			"card_number", "payed_at", "refund_status", "refunded_at",
			"psp_product_id", "psp_product_desc", "psp_price_id",
			"psp_payment_id", "psp_customer_id", "psp_customer_email",
			"psp_customer_name", "psp_customer_country", "psp_subscription_id",
			"updated_at").
		Updates(model)

	if result.Error != nil {
		return fmt.Errorf("failed to update order in transaction: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with ID %d not found or already deleted", order.ID)
	}

	return nil
}

// GetByOrderIDWithTransaction 在事务中根据订单ID获取订单记录
func (r *orderRepository) GetByOrderIDWithTransaction(tx any, orderID string) (*domain.Order, error) {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return nil, fmt.Errorf("invalid transaction type")
	}

	var model db.OrderModel

	if err := gormTx.Where("order_id = ? AND deleted = 0", orderID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order with order ID %s not found", orderID)
		}
		return nil, fmt.Errorf("failed to get order by order ID in transaction: %w", err)
	}

	return model.ToDomain(), nil
}

// GetByIDWithTransaction 在事务中根据ID获取订单记录
func (r *orderRepository) GetByIDWithTransaction(tx any, id uint64) (*domain.Order, error) {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return nil, fmt.Errorf("invalid transaction type")
	}

	var model db.OrderModel

	if err := gormTx.Where("id = ? AND deleted = 0", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order with ID %d not found", id)
		}
		return nil, fmt.Errorf("failed to get order by ID in transaction: %w", err)
	}

	return model.ToDomain(), nil
}

// UpdateStatusWithTransaction 在事务中更新订单支付状态
func (r *orderRepository) UpdateStatusSucceededWithTransaction(tx any,
	orderID string, payStatus string, payRet string, newSnapshot string) error {

	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	var result *gorm.DB

	if len(newSnapshot) > 0 {
		result = gormTx.Model(&db.OrderModel{}).
			Where("order_id = ? AND deleted = 0", orderID).
			Updates(map[string]interface{}{
				"product_snapshot": newSnapshot,
				"pay_status":       payStatus,
				"pay_ret":          payRet,
				"payed_at":         gorm.Expr("NOW()"),
				"updated_at":       gorm.Expr("NOW()"),
			})

	} else {
		result = gormTx.Model(&db.OrderModel{}).
			Where("order_id = ? AND deleted = 0", orderID).
			Updates(map[string]interface{}{
				"pay_status": payStatus,
				"pay_ret":    payRet,
				"payed_at":   gorm.Expr("NOW()"),
				"updated_at": gorm.Expr("NOW()"),
			})
	}

	if result.Error != nil {
		return fmt.Errorf("failed to update order status in transaction: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}

// UpdatePaymentID 在事务中更新订单支付ID
func (r *orderRepository) UpdatePaymentIDWithTransaction(tx any, orderID string, paymentID string) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	result := gormTx.Model(&db.OrderModel{}).
		Where("order_id = ? AND deleted = 0", orderID).
		Updates(map[string]interface{}{
			"psp_payment_id": paymentID,
			"updated_at":     gorm.Expr("NOW()"),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to update psp_payment_id: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}

// UpdateStatusOtherWithTransaction 在事务中更新订单支付状态
func (r *orderRepository) UpdateStatusOtherWithTransaction(tx any, orderID string, payStatus string, payRet string) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	result := gormTx.Model(&db.OrderModel{}).
		Where("order_id = ? AND deleted = 0", orderID).
		Updates(map[string]interface{}{
			"pay_status": payStatus,
			"pay_ret":    payRet,
			"updated_at": gorm.Expr("NOW()"),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to update order status in transaction: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}

// UpdateRefundStatusFailedWithTransaction 在事务中更新退款失败状态
func (r *orderRepository) UpdateRefundStatusFailedWithTransaction(tx any, orderID string, refundStatus string, refundRet string) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	result := gormTx.Model(&db.OrderModel{}).
		Where("order_id = ? AND deleted = 0", orderID).
		Updates(map[string]interface{}{
			"refund_status":          refundStatus,
			"psp_payment_refund_ret": refundRet,
			"updated_at":             gorm.Expr("NOW()"),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to update order status in transaction: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}

// UpdateRefundStatusFailedWithTransaction 在事务中更新退款成功状态
func (r *orderRepository) UpdateRefundSucceededWithTransaction(tx any, orderID string, refundStatus string, refundRet string) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	result := gormTx.Model(&db.OrderModel{}).
		Where("order_id = ? AND deleted = 0", orderID).
		Updates(map[string]interface{}{
			"refund_status":          refundStatus,
			"psp_payment_refund_ret": refundRet,
			"refunded_at":            gorm.Expr("NOW()"),
			"updated_at":             gorm.Expr("NOW()"),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to update order status in transaction: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}

// SoftDeleteWithTransaction 在事务中软删除订单记录
func (r *orderRepository) SoftDeleteWithTransaction(tx any, orderID string) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("invalid transaction type")
	}

	result := gormTx.Model(&db.OrderModel{}).
		Where("order_id = ? AND deleted = 0", orderID).
		Updates(map[string]interface{}{
			"deleted":    1,
			"updated_at": gorm.Expr("NOW()"),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to soft delete order in transaction: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order with order ID %s not found or already deleted", orderID)
	}

	return nil
}
