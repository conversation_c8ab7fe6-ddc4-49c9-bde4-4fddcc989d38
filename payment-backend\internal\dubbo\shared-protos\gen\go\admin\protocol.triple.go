// Code generated by protoc-gen-triple. DO NOT EDIT.
//
// Source: protos/protocol.proto
package admin

import (
	"context"
)

import (
	"dubbo.apache.org/dubbo-go/v3"
	"dubbo.apache.org/dubbo-go/v3/client"
	"dubbo.apache.org/dubbo-go/v3/common"
	"dubbo.apache.org/dubbo-go/v3/common/constant"
	"dubbo.apache.org/dubbo-go/v3/protocol/triple/triple_protocol"
	"dubbo.apache.org/dubbo-go/v3/server"
)

// This is a compile-time assertion to ensure that this generated file and the Triple package
// are compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of Triple newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of Triple or updating the Triple
// version compiled into your binary.
const _ = triple_protocol.IsAtLeastVersion0_1_0

const (
	// ProtocolServiceName is the fully-qualified name of the ProtocolService service.
	ProtocolServiceName = "com.aibook.admin.grpc.ProtocolService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// ProtocolServicegetProtocolProcedure is the fully-qualified name of the ProtocolService's getProtocol RPC.
	ProtocolServicegetProtocolProcedure = "/com.aibook.admin.grpc.ProtocolService/getProtocol"
	// ProtocolServiceconfirmProtocolProcedure is the fully-qualified name of the ProtocolService's confirmProtocol RPC.
	ProtocolServiceconfirmProtocolProcedure = "/com.aibook.admin.grpc.ProtocolService/confirmProtocol"
)

var (
	_ ProtocolService = (*ProtocolServiceImpl)(nil)
)

// ProtocolService is a client for the com.aibook.admin.grpc.ProtocolService service.
type ProtocolService interface {
	GetProtocol(ctx context.Context, req *ProtocolRequest, opts ...client.CallOption) (*ProtocolResponse, error)
	ConfirmProtocol(ctx context.Context, req *ConfirmProtocolRequest, opts ...client.CallOption) (*ConfirmProtocolResponse, error)
}

// NewProtocolService constructs a client for the admin.ProtocolService service.
func NewProtocolService(cli *client.Client, opts ...client.ReferenceOption) (ProtocolService, error) {
	conn, err := cli.DialWithInfo("com.aibook.admin.grpc.ProtocolService", &ProtocolService_ClientInfo, opts...)
	if err != nil {
		return nil, err
	}
	return &ProtocolServiceImpl{
		conn: conn,
	}, nil
}

func SetConsumerProtocolService(srv common.RPCService) {
	dubbo.SetConsumerServiceWithInfo(srv, &ProtocolService_ClientInfo)
}

// ProtocolServiceImpl implements ProtocolService.
type ProtocolServiceImpl struct {
	conn *client.Connection
}

func (c *ProtocolServiceImpl) GetProtocol(ctx context.Context, req *ProtocolRequest, opts ...client.CallOption) (*ProtocolResponse, error) {
	resp := new(ProtocolResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "getProtocol", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *ProtocolServiceImpl) ConfirmProtocol(ctx context.Context, req *ConfirmProtocolRequest, opts ...client.CallOption) (*ConfirmProtocolResponse, error) {
	resp := new(ConfirmProtocolResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "confirmProtocol", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

var ProtocolService_ClientInfo = client.ClientInfo{
	InterfaceName: "com.aibook.admin.grpc.ProtocolService",
	MethodNames:   []string{"getProtocol", "confirmProtocol"},
	ConnectionInjectFunc: func(dubboCliRaw interface{}, conn *client.Connection) {
		dubboCli := dubboCliRaw.(*ProtocolServiceImpl)
		dubboCli.conn = conn
	},
}

// ProtocolServiceHandler is an implementation of the com.aibook.admin.grpc.ProtocolService service.
type ProtocolServiceHandler interface {
	GetProtocol(context.Context, *ProtocolRequest) (*ProtocolResponse, error)
	ConfirmProtocol(context.Context, *ConfirmProtocolRequest) (*ConfirmProtocolResponse, error)
}

func RegisterProtocolServiceHandler(srv *server.Server, hdlr ProtocolServiceHandler, opts ...server.ServiceOption) error {
	return srv.Register(hdlr, &ProtocolService_ServiceInfo, opts...)
}

func SetProviderProtocolService(srv common.RPCService) {
	dubbo.SetProviderServiceWithInfo(srv, &ProtocolService_ServiceInfo)
}

var ProtocolService_ServiceInfo = server.ServiceInfo{
	InterfaceName: "com.aibook.admin.grpc.ProtocolService",
	ServiceType:   (*ProtocolServiceHandler)(nil),
	Methods: []server.MethodInfo{
		{
			Name: "getProtocol",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ProtocolRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ProtocolRequest)
				res, err := handler.(ProtocolServiceHandler).GetProtocol(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "confirmProtocol",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ConfirmProtocolRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ConfirmProtocolRequest)
				res, err := handler.(ProtocolServiceHandler).ConfirmProtocol(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
	},
}
