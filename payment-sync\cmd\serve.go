/*
Copyright © 2025 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"payment-sync/internal/app"
	"syscall"

	"github.com/spf13/cobra"
)

// serveCmd represents the serve command
var serveCmd = &cobra.Command{
	Use:   "serve",
	Short: "启动交易同步服务",
	Long:  `启动交易同步后端服务器`,
	Run: func(cmd *cobra.Command, args []string) {
		podIP := os.Getenv("POD_IP")
		if len(podIP) > 0 {
			os.Setenv("PAYMENT_DUBBO_IP", podIP)
		}
		fmt.Printf("podIP:%v\n",
			podIP)

		// 创建fx应用
		fxApp := app.NewApp()

		// 创建上下文用于优雅关闭
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		// 监听系统信号
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sig<PERSON>han, syscall.SIGINT, syscall.SIGTERM)

		// 启动应用
		if err := fxApp.Start(ctx); err != nil {
			panic(err)
		}

		// 等待关闭信号
		<-sigChan

		// 优雅关闭
		if err := fxApp.Stop(ctx); err != nil {
			panic(err)
		}
	},
}

func init() {
	rootCmd.AddCommand(serveCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// serveCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// serveCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
}
