// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v6.31.1
// source: protos/market.proto

package marketpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MarketService_ShareBook_FullMethodName              = "/com.aibook.market.grpc.MarketService/ShareBook"
	MarketService_BookDetail_FullMethodName             = "/com.aibook.market.grpc.MarketService/BookDetail"
	MarketService_BookModify_FullMethodName             = "/com.aibook.market.grpc.MarketService/BookModify"
	MarketService_BookOptLogs_FullMethodName            = "/com.aibook.market.grpc.MarketService/BookOptLogs"
	MarketService_SearchBook_FullMethodName             = "/com.aibook.market.grpc.MarketService/SearchBook"
	MarketService_AdminSearchBook_FullMethodName        = "/com.aibook.market.grpc.MarketService/AdminSearchBook"
	MarketService_ThemeTopk_FullMethodName              = "/com.aibook.market.grpc.MarketService/ThemeTopk"
	MarketService_ThemeBookTopk_FullMethodName          = "/com.aibook.market.grpc.MarketService/ThemeBookTopk"
	MarketService_GetThemeBooks_FullMethodName          = "/com.aibook.market.grpc.MarketService/GetThemeBooks"
	MarketService_GetThemeList_FullMethodName           = "/com.aibook.market.grpc.MarketService/GetThemeList"
	MarketService_GetRecommendBooks_FullMethodName      = "/com.aibook.market.grpc.MarketService/GetRecommendBooks"
	MarketService_GetBookDownloadRecord_FullMethodName  = "/com.aibook.market.grpc.MarketService/GetBookDownloadRecord"
	MarketService_SyncBookDownloadRecord_FullMethodName = "/com.aibook.market.grpc.MarketService/SyncBookDownloadRecord"
)

// MarketServiceClient is the client API for MarketService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MarketServiceClient interface {
	// 共享绘本
	ShareBook(ctx context.Context, in *ShareBookRequest, opts ...grpc.CallOption) (*Empty, error)
	// 绘本详情
	BookDetail(ctx context.Context, in *BookId, opts ...grpc.CallOption) (*BookInfo, error)
	// 绘本属性修改(如审核、发布、下架, 删除) -- 管理平台调用
	BookModify(ctx context.Context, in *BookModifyRequest, opts ...grpc.CallOption) (*Empty, error)
	// 操作日志查询 --- 管理平台调用
	BookOptLogs(ctx context.Context, in *BookIdPage, opts ...grpc.CallOption) (*BookOptLogsResponse, error)
	// 搜索绘本（按主题、按标题）， 按热度排序返回 --- 绘本终端调用
	SearchBook(ctx context.Context, in *SearchBookRequest, opts ...grpc.CallOption) (*BookListPageResponse, error)
	// 管理平台搜索绘本 --- 管理平台调用
	AdminSearchBook(ctx context.Context, in *AdminSearchBookRequest, opts ...grpc.CallOption) (*BookListPageResponse, error)
	// 绘本主题 topK (按国家和年龄段筛选， 全量返回) --- 绘本终端调用
	ThemeTopk(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ThemeListResponse, error)
	// 绘本主题下绘本 topK (按国家和年龄段筛选， 全量返回) --- 绘本终端调用
	ThemeBookTopk(ctx context.Context, in *ThemeIdRequest, opts ...grpc.CallOption) (*BookList, error)
	// 绘本主题下绘本列表(按国家和年龄段筛选， 分页返回) --- 绘本终端调用
	GetThemeBooks(ctx context.Context, in *ThemeBooksPageRequest, opts ...grpc.CallOption) (*BookListPageResponse, error)
	// 绘本主题列表(全量返回) --- 管理平台调用
	GetThemeList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ThemeDetailListResponse, error)
	// 推荐绘本查询(根据国家和年龄段筛选) --- 绘本终端调用
	GetRecommendBooks(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*BookList, error)
	// 查询绘本下载记录 --- 管理平台调用
	GetBookDownloadRecord(ctx context.Context, in *UserIdPageRequest, opts ...grpc.CallOption) (*BookDownloadRecordResponse, error)
	// 同步下载记录 --- 绘本终端调用
	SyncBookDownloadRecord(ctx context.Context, in *BookIdRequest, opts ...grpc.CallOption) (*Empty, error)
}

type marketServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMarketServiceClient(cc grpc.ClientConnInterface) MarketServiceClient {
	return &marketServiceClient{cc}
}

func (c *marketServiceClient) ShareBook(ctx context.Context, in *ShareBookRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, MarketService_ShareBook_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BookDetail(ctx context.Context, in *BookId, opts ...grpc.CallOption) (*BookInfo, error) {
	out := new(BookInfo)
	err := c.cc.Invoke(ctx, MarketService_BookDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BookModify(ctx context.Context, in *BookModifyRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, MarketService_BookModify_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BookOptLogs(ctx context.Context, in *BookIdPage, opts ...grpc.CallOption) (*BookOptLogsResponse, error) {
	out := new(BookOptLogsResponse)
	err := c.cc.Invoke(ctx, MarketService_BookOptLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SearchBook(ctx context.Context, in *SearchBookRequest, opts ...grpc.CallOption) (*BookListPageResponse, error) {
	out := new(BookListPageResponse)
	err := c.cc.Invoke(ctx, MarketService_SearchBook_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) AdminSearchBook(ctx context.Context, in *AdminSearchBookRequest, opts ...grpc.CallOption) (*BookListPageResponse, error) {
	out := new(BookListPageResponse)
	err := c.cc.Invoke(ctx, MarketService_AdminSearchBook_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) ThemeTopk(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ThemeListResponse, error) {
	out := new(ThemeListResponse)
	err := c.cc.Invoke(ctx, MarketService_ThemeTopk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) ThemeBookTopk(ctx context.Context, in *ThemeIdRequest, opts ...grpc.CallOption) (*BookList, error) {
	out := new(BookList)
	err := c.cc.Invoke(ctx, MarketService_ThemeBookTopk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) GetThemeBooks(ctx context.Context, in *ThemeBooksPageRequest, opts ...grpc.CallOption) (*BookListPageResponse, error) {
	out := new(BookListPageResponse)
	err := c.cc.Invoke(ctx, MarketService_GetThemeBooks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) GetThemeList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ThemeDetailListResponse, error) {
	out := new(ThemeDetailListResponse)
	err := c.cc.Invoke(ctx, MarketService_GetThemeList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) GetRecommendBooks(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*BookList, error) {
	out := new(BookList)
	err := c.cc.Invoke(ctx, MarketService_GetRecommendBooks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) GetBookDownloadRecord(ctx context.Context, in *UserIdPageRequest, opts ...grpc.CallOption) (*BookDownloadRecordResponse, error) {
	out := new(BookDownloadRecordResponse)
	err := c.cc.Invoke(ctx, MarketService_GetBookDownloadRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SyncBookDownloadRecord(ctx context.Context, in *BookIdRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, MarketService_SyncBookDownloadRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MarketServiceServer is the server API for MarketService service.
// All implementations must embed UnimplementedMarketServiceServer
// for forward compatibility
type MarketServiceServer interface {
	// 共享绘本
	ShareBook(context.Context, *ShareBookRequest) (*Empty, error)
	// 绘本详情
	BookDetail(context.Context, *BookId) (*BookInfo, error)
	// 绘本属性修改(如审核、发布、下架, 删除) -- 管理平台调用
	BookModify(context.Context, *BookModifyRequest) (*Empty, error)
	// 操作日志查询 --- 管理平台调用
	BookOptLogs(context.Context, *BookIdPage) (*BookOptLogsResponse, error)
	// 搜索绘本（按主题、按标题）， 按热度排序返回 --- 绘本终端调用
	SearchBook(context.Context, *SearchBookRequest) (*BookListPageResponse, error)
	// 管理平台搜索绘本 --- 管理平台调用
	AdminSearchBook(context.Context, *AdminSearchBookRequest) (*BookListPageResponse, error)
	// 绘本主题 topK (按国家和年龄段筛选， 全量返回) --- 绘本终端调用
	ThemeTopk(context.Context, *Empty) (*ThemeListResponse, error)
	// 绘本主题下绘本 topK (按国家和年龄段筛选， 全量返回) --- 绘本终端调用
	ThemeBookTopk(context.Context, *ThemeIdRequest) (*BookList, error)
	// 绘本主题下绘本列表(按国家和年龄段筛选， 分页返回) --- 绘本终端调用
	GetThemeBooks(context.Context, *ThemeBooksPageRequest) (*BookListPageResponse, error)
	// 绘本主题列表(全量返回) --- 管理平台调用
	GetThemeList(context.Context, *Empty) (*ThemeDetailListResponse, error)
	// 推荐绘本查询(根据国家和年龄段筛选) --- 绘本终端调用
	GetRecommendBooks(context.Context, *Empty) (*BookList, error)
	// 查询绘本下载记录 --- 管理平台调用
	GetBookDownloadRecord(context.Context, *UserIdPageRequest) (*BookDownloadRecordResponse, error)
	// 同步下载记录 --- 绘本终端调用
	SyncBookDownloadRecord(context.Context, *BookIdRequest) (*Empty, error)
	mustEmbedUnimplementedMarketServiceServer()
}

// UnimplementedMarketServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMarketServiceServer struct {
}

func (UnimplementedMarketServiceServer) ShareBook(context.Context, *ShareBookRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShareBook not implemented")
}
func (UnimplementedMarketServiceServer) BookDetail(context.Context, *BookId) (*BookInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BookDetail not implemented")
}
func (UnimplementedMarketServiceServer) BookModify(context.Context, *BookModifyRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BookModify not implemented")
}
func (UnimplementedMarketServiceServer) BookOptLogs(context.Context, *BookIdPage) (*BookOptLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BookOptLogs not implemented")
}
func (UnimplementedMarketServiceServer) SearchBook(context.Context, *SearchBookRequest) (*BookListPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchBook not implemented")
}
func (UnimplementedMarketServiceServer) AdminSearchBook(context.Context, *AdminSearchBookRequest) (*BookListPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminSearchBook not implemented")
}
func (UnimplementedMarketServiceServer) ThemeTopk(context.Context, *Empty) (*ThemeListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThemeTopk not implemented")
}
func (UnimplementedMarketServiceServer) ThemeBookTopk(context.Context, *ThemeIdRequest) (*BookList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThemeBookTopk not implemented")
}
func (UnimplementedMarketServiceServer) GetThemeBooks(context.Context, *ThemeBooksPageRequest) (*BookListPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetThemeBooks not implemented")
}
func (UnimplementedMarketServiceServer) GetThemeList(context.Context, *Empty) (*ThemeDetailListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetThemeList not implemented")
}
func (UnimplementedMarketServiceServer) GetRecommendBooks(context.Context, *Empty) (*BookList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecommendBooks not implemented")
}
func (UnimplementedMarketServiceServer) GetBookDownloadRecord(context.Context, *UserIdPageRequest) (*BookDownloadRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookDownloadRecord not implemented")
}
func (UnimplementedMarketServiceServer) SyncBookDownloadRecord(context.Context, *BookIdRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncBookDownloadRecord not implemented")
}
func (UnimplementedMarketServiceServer) mustEmbedUnimplementedMarketServiceServer() {}

// UnsafeMarketServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MarketServiceServer will
// result in compilation errors.
type UnsafeMarketServiceServer interface {
	mustEmbedUnimplementedMarketServiceServer()
}

func RegisterMarketServiceServer(s grpc.ServiceRegistrar, srv MarketServiceServer) {
	s.RegisterService(&MarketService_ServiceDesc, srv)
}

func _MarketService_ShareBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShareBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).ShareBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_ShareBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).ShareBook(ctx, req.(*ShareBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BookDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BookDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BookDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BookDetail(ctx, req.(*BookId))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BookModify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookModifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BookModify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BookModify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BookModify(ctx, req.(*BookModifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BookOptLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookIdPage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BookOptLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BookOptLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BookOptLogs(ctx, req.(*BookIdPage))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SearchBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SearchBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_SearchBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SearchBook(ctx, req.(*SearchBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_AdminSearchBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminSearchBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).AdminSearchBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_AdminSearchBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).AdminSearchBook(ctx, req.(*AdminSearchBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_ThemeTopk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).ThemeTopk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_ThemeTopk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).ThemeTopk(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_ThemeBookTopk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThemeIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).ThemeBookTopk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_ThemeBookTopk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).ThemeBookTopk(ctx, req.(*ThemeIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_GetThemeBooks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThemeBooksPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).GetThemeBooks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_GetThemeBooks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).GetThemeBooks(ctx, req.(*ThemeBooksPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_GetThemeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).GetThemeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_GetThemeList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).GetThemeList(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_GetRecommendBooks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).GetRecommendBooks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_GetRecommendBooks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).GetRecommendBooks(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_GetBookDownloadRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).GetBookDownloadRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_GetBookDownloadRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).GetBookDownloadRecord(ctx, req.(*UserIdPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SyncBookDownloadRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SyncBookDownloadRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_SyncBookDownloadRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SyncBookDownloadRecord(ctx, req.(*BookIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MarketService_ServiceDesc is the grpc.ServiceDesc for MarketService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MarketService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "com.aibook.market.grpc.MarketService",
	HandlerType: (*MarketServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ShareBook",
			Handler:    _MarketService_ShareBook_Handler,
		},
		{
			MethodName: "BookDetail",
			Handler:    _MarketService_BookDetail_Handler,
		},
		{
			MethodName: "BookModify",
			Handler:    _MarketService_BookModify_Handler,
		},
		{
			MethodName: "BookOptLogs",
			Handler:    _MarketService_BookOptLogs_Handler,
		},
		{
			MethodName: "SearchBook",
			Handler:    _MarketService_SearchBook_Handler,
		},
		{
			MethodName: "AdminSearchBook",
			Handler:    _MarketService_AdminSearchBook_Handler,
		},
		{
			MethodName: "ThemeTopk",
			Handler:    _MarketService_ThemeTopk_Handler,
		},
		{
			MethodName: "ThemeBookTopk",
			Handler:    _MarketService_ThemeBookTopk_Handler,
		},
		{
			MethodName: "GetThemeBooks",
			Handler:    _MarketService_GetThemeBooks_Handler,
		},
		{
			MethodName: "GetThemeList",
			Handler:    _MarketService_GetThemeList_Handler,
		},
		{
			MethodName: "GetRecommendBooks",
			Handler:    _MarketService_GetRecommendBooks_Handler,
		},
		{
			MethodName: "GetBookDownloadRecord",
			Handler:    _MarketService_GetBookDownloadRecord_Handler,
		},
		{
			MethodName: "SyncBookDownloadRecord",
			Handler:    _MarketService_SyncBookDownloadRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/market.proto",
}
