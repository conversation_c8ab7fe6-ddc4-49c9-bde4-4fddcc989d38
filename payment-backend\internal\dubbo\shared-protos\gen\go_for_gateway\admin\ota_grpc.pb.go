//*
// App在线升级(OTA)服务定义

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: protos/ota.proto

package admin

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	OtaService_CheckUpdate_FullMethodName         = "/com.aibook.admin.grpc.OtaService/CheckUpdate"
	OtaService_ReportUpgradeResult_FullMethodName = "/com.aibook.admin.grpc.OtaService/ReportUpgradeResult"
)

// OtaServiceClient is the client API for OtaService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// OTA服务
type OtaServiceClient interface {
	// 检查更新
	CheckUpdate(ctx context.Context, in *CheckUpdateRequest, opts ...grpc.CallOption) (*CheckUpdateResponse, error)
	// 上报升级结果
	ReportUpgradeResult(ctx context.Context, in *ReportUpgradeResultRequest, opts ...grpc.CallOption) (*ReportUpgradeResultResponse, error)
}

type otaServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOtaServiceClient(cc grpc.ClientConnInterface) OtaServiceClient {
	return &otaServiceClient{cc}
}

func (c *otaServiceClient) CheckUpdate(ctx context.Context, in *CheckUpdateRequest, opts ...grpc.CallOption) (*CheckUpdateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckUpdateResponse)
	err := c.cc.Invoke(ctx, OtaService_CheckUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *otaServiceClient) ReportUpgradeResult(ctx context.Context, in *ReportUpgradeResultRequest, opts ...grpc.CallOption) (*ReportUpgradeResultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReportUpgradeResultResponse)
	err := c.cc.Invoke(ctx, OtaService_ReportUpgradeResult_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OtaServiceServer is the server API for OtaService service.
// All implementations must embed UnimplementedOtaServiceServer
// for forward compatibility.
//
// OTA服务
type OtaServiceServer interface {
	// 检查更新
	CheckUpdate(context.Context, *CheckUpdateRequest) (*CheckUpdateResponse, error)
	// 上报升级结果
	ReportUpgradeResult(context.Context, *ReportUpgradeResultRequest) (*ReportUpgradeResultResponse, error)
	mustEmbedUnimplementedOtaServiceServer()
}

// UnimplementedOtaServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOtaServiceServer struct{}

func (UnimplementedOtaServiceServer) CheckUpdate(context.Context, *CheckUpdateRequest) (*CheckUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUpdate not implemented")
}
func (UnimplementedOtaServiceServer) ReportUpgradeResult(context.Context, *ReportUpgradeResultRequest) (*ReportUpgradeResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportUpgradeResult not implemented")
}
func (UnimplementedOtaServiceServer) mustEmbedUnimplementedOtaServiceServer() {}
func (UnimplementedOtaServiceServer) testEmbeddedByValue()                    {}

// UnsafeOtaServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OtaServiceServer will
// result in compilation errors.
type UnsafeOtaServiceServer interface {
	mustEmbedUnimplementedOtaServiceServer()
}

func RegisterOtaServiceServer(s grpc.ServiceRegistrar, srv OtaServiceServer) {
	// If the following call pancis, it indicates UnimplementedOtaServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&OtaService_ServiceDesc, srv)
}

func _OtaService_CheckUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OtaServiceServer).CheckUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OtaService_CheckUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OtaServiceServer).CheckUpdate(ctx, req.(*CheckUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OtaService_ReportUpgradeResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportUpgradeResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OtaServiceServer).ReportUpgradeResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OtaService_ReportUpgradeResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OtaServiceServer).ReportUpgradeResult(ctx, req.(*ReportUpgradeResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OtaService_ServiceDesc is the grpc.ServiceDesc for OtaService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OtaService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "com.aibook.admin.grpc.OtaService",
	HandlerType: (*OtaServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckUpdate",
			Handler:    _OtaService_CheckUpdate_Handler,
		},
		{
			MethodName: "ReportUpgradeResult",
			Handler:    _OtaService_ReportUpgradeResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/ota.proto",
}
