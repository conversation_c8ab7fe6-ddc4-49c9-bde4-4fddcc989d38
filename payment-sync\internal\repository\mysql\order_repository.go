package mysql

import (
	"fmt"
	"time"

	"payment-sync/internal/domain"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// OrderModel 订单数据库模型（简化版）
type OrderModel struct {
	ID                 uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	OrderID            string     `gorm:"type:varchar(64);not null;uniqueIndex:idx_orders_order_id" json:"order_id"`
	UserID             string     `gorm:"type:varchar(64);not null;index:idx_orders_user_id" json:"user_id"`
	ProductID          string     `gorm:"type:varchar(64);not null;index:idx_orders_product_id" json:"product_id"`
	ProductDesc        string     `gorm:"type:text" json:"product_desc"`
	ProductSnapshot    string     `gorm:"type:text" json:"product_snapshot"`
	PriceID            string     `gorm:"type:varchar(64);not null;index:idx_orders_price_id" json:"price_id"`
	Quantity           uint32     `gorm:"not null;default:1" json:"quantity"`
	Amount             string     `gorm:"type:decimal(18,2);not null" json:"amount"`
	NetAmount          string     `gorm:"type:decimal(18,2)" json:"net_amount"`
	Currency           string     `gorm:"type:char(3);index:idx_currency" json:"currency"`
	PayStatus          string     `gorm:"type:varchar(20);not null;index:idx_orders_pay_status;default:'created'" json:"pay_status"`
	PayRet             string     `gorm:"type:varchar(512)" json:"pay_ret"`
	PayedMethod        string     `gorm:"type:varchar(32)" json:"payed_method"`
	PSPProvider        string     `gorm:"type:varchar(32);not null;index:idx_psp_provider" json:"psp_provider"`
	CardNumber         string     `gorm:"type:varchar(32)" json:"card_number"`
	PayedAt            *time.Time `gorm:"index:idx_payed_at" json:"payed_at"`
	RefundStatus       string     `gorm:"type:varchar(20);not null;index:idx_orders_refund_status;default:'none'" json:"refund_status"`
	RefundedAt         *time.Time `gorm:"index:idx_refunded_at" json:"refunded_at"`
	PSPPaymentID       string     `gorm:"type:varchar(128);index:idx_orders_psp_payment_id" json:"psp_payment_id"`
	PSPPaymentIntentID string     `gorm:"type:varchar(128);index:idx_orders_psp_payment_intent_id" json:"psp_payment_intent_id"`
	PSPCustomerID      string     `gorm:"type:varchar(128);index:idx_orders_psp_customer_id" json:"psp_customer_id"`
	PSPCustomerEmail   string     `gorm:"type:varchar(128);index:idx_orders_psp_customer_email" json:"psp_customer_email"`
	PSPCustomerName    string     `gorm:"type:varchar(128);index:idx_orders_psp_customer_name" json:"psp_customer_name"`
	PSPCustomerCountry string     `gorm:"type:varchar(128);index:idx_orders_psp_customer_country" json:"psp_customer_country"`
	CreatedAt          *time.Time `gorm:"not null;index:idx_orders_created_at" json:"created_at"`
	UpdatedAt          *time.Time `gorm:"not null" json:"updated_at"`
	Deleted            uint8      `gorm:"not null;default:0;index:idx_orders_deleted" json:"deleted"`
}

// TableName 指定表名
func (OrderModel) TableName() string {
	return "payment_orders"
}

// ToDomain 转换为domain模型
func (o *OrderModel) ToDomain() *domain.Order {
	amount, _ := decimal.NewFromString(o.Amount)
	netAmount, _ := decimal.NewFromString(o.NetAmount)

	return &domain.Order{
		ID:                 o.ID,
		OrderID:            o.OrderID,
		UserID:             o.UserID,
		ProductID:          o.ProductID,
		ProductDesc:        o.ProductDesc,
		ProductSnapshot:    o.ProductSnapshot,
		PriceID:            o.PriceID,
		Quantity:           o.Quantity,
		Amount:             amount,
		NetAmount:          netAmount,
		Currency:           o.Currency,
		PayStatus:          o.PayStatus,
		PayRet:             o.PayRet,
		PayedMethod:        o.PayedMethod,
		PSPProvider:        o.PSPProvider,
		CardNumber:         o.CardNumber,
		PayedAt:            o.PayedAt,
		RefundStatus:       o.RefundStatus,
		RefundedAt:         o.RefundedAt,
		PSPPaymentID:       o.PSPPaymentID,
		PSPPaymentIntentID: o.PSPPaymentIntentID,
		PSPCustomerID:      o.PSPCustomerID,
		PSPCustomerEmail:   o.PSPCustomerEmail,
		PSPCustomerName:    o.PSPCustomerName,
		PSPCustomerCountry: o.PSPCustomerCountry,
		CreatedAt:          o.CreatedAt,
		UpdatedAt:          o.UpdatedAt,
	}
}

// orderRepository MySQL订单仓储实现
type orderRepository struct {
	db *gorm.DB
}

// NewOrderRepository 创建MySQL订单仓储
func NewOrderRepository(database *gorm.DB) domain.OrderRepository {
	return &orderRepository{
		db: database,
	}
}

// GetByOrderID 根据订单ID获取订单
func (r *orderRepository) GetByOrderID(orderID string) (*domain.Order, error) {
	var model OrderModel
	if err := r.db.Where("order_id = ? AND deleted = 0", orderID).First(&model).Error; err != nil {
		return nil, fmt.Errorf("failed to get order by ID: %w", err)
	}
	return model.ToDomain(), nil
}

// GetByOrderIDForUpdate 根据订单ID获取订单（加锁）
func (r *orderRepository) GetByOrderIDForUpdate(tx any, orderID string) (*domain.Order, error) {
	var model OrderModel
	db := tx.(*gorm.DB)
	if err := db.Where("order_id = ? AND deleted = 0", orderID).First(&model).Error; err != nil {
		return nil, fmt.Errorf("failed to get order by ID for update: %w", err)
	}
	return model.ToDomain(), nil
}

// UpdateWithTransaction 在事务中更新订单
func (r *orderRepository) UpdateWithTransaction(tx any, order *domain.Order) error {
	db := tx.(*gorm.DB)
	model := r.fromDomain(order)
	if err := db.Save(model).Error; err != nil {
		return fmt.Errorf("failed to update order: %w", err)
	}
	return nil
}

// UpdateStatusSucceededWithTransaction 在事务中更新订单状态为成功
func (r *orderRepository) UpdateStatusSucceededWithTransaction(tx any, orderID string, payStatus string, refundRet string, newSnapshot string) error {
	db := tx.(*gorm.DB)
	updates := map[string]interface{}{
		"pay_status": payStatus,
		"pay_ret":    refundRet,
	}
	if newSnapshot != "" {
		updates["product_snapshot"] = newSnapshot
	}
	if err := db.Model(&OrderModel{}).Where("order_id = ?", orderID).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update order status succeeded: %w", err)
	}
	return nil
}

// UpdateRefundSucceededWithTransaction 在事务中更新退款状态为成功
func (r *orderRepository) UpdateRefundSucceededWithTransaction(tx any, orderID string, refundStatus string, refundRet string) error {
	db := tx.(*gorm.DB)
	updates := map[string]interface{}{
		"refund_status": refundStatus,
		"pay_ret":       refundRet,
	}
	if err := db.Model(&OrderModel{}).Where("order_id = ?", orderID).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update refund status succeeded: %w", err)
	}
	return nil
}

// GetOrdersForPaymentSync 获取需要付款同步的订单
func (r *orderRepository) GetOrdersForPaymentSync(startTime, endTime time.Time) ([]*domain.Order, error) {
	var models []OrderModel

	// 查询条件：pay_status != "fulfilled" 且 refund_status == "none" 且在时间范围内
	query := r.db.Where("deleted = 0").
		Where("pay_status != ?", "fulfilled").
		Where("refund_status = ?", "none").
		Where("created_at >= ? AND created_at <= ?", startTime, endTime).
		Order("created_at ASC")

	if err := query.Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to get orders for payment sync: %w", err)
	}

	// 转换为domain模型
	orders := make([]*domain.Order, len(models))
	for i, model := range models {
		orders[i] = model.ToDomain()
	}

	return orders, nil
}

// GetOrdersForRefundSync 获取需要退款同步的订单
func (r *orderRepository) GetOrdersForRefundSync(startTime, endTime time.Time) ([]*domain.Order, error) {
	var models []OrderModel

	// 查询条件：pay_status == "fulfilled" 且 refund_status != "fulfilled" 且在时间范围内
	query := r.db.Where("deleted = 0").
		Where("pay_status = ?", "fulfilled").
		Where("refund_status != ?", "fulfilled").
		Where("refund_status != ?", "none").
		Where("created_at >= ? AND created_at <= ?", startTime, endTime).
		Order("created_at ASC")

	if err := query.Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to get orders for refund sync: %w", err)
	}

	// 转换为domain模型
	orders := make([]*domain.Order, len(models))
	for i, model := range models {
		orders[i] = model.ToDomain()
	}

	return orders, nil
}

// fromDomain 从domain模型转换为数据库模型
func (r *orderRepository) fromDomain(order *domain.Order) *OrderModel {
	return &OrderModel{
		ID:                 order.ID,
		OrderID:            order.OrderID,
		UserID:             order.UserID,
		ProductID:          order.ProductID,
		ProductDesc:        order.ProductDesc,
		ProductSnapshot:    order.ProductSnapshot,
		PriceID:            order.PriceID,
		Quantity:           order.Quantity,
		Amount:             order.Amount.String(),
		NetAmount:          order.NetAmount.String(),
		Currency:           order.Currency,
		PayStatus:          order.PayStatus,
		PayRet:             order.PayRet,
		PayedMethod:        order.PayedMethod,
		PSPProvider:        order.PSPProvider,
		CardNumber:         order.CardNumber,
		PayedAt:            order.PayedAt,
		RefundStatus:       order.RefundStatus,
		RefundedAt:         order.RefundedAt,
		PSPPaymentID:       order.PSPPaymentID,
		PSPPaymentIntentID: order.PSPPaymentIntentID,
		PSPCustomerID:      order.PSPCustomerID,
		PSPCustomerEmail:   order.PSPCustomerEmail,
		PSPCustomerName:    order.PSPCustomerName,
		PSPCustomerCountry: order.PSPCustomerCountry,
		CreatedAt:          order.CreatedAt,
		UpdatedAt:          order.UpdatedAt,
		Deleted:            0, // 假设未删除
	}
}
