# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from protos import ota_pb2 as protos_dot_ota__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in protos/ota_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class OtaServiceStub(object):
    """OTA服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CheckUpdate = channel.unary_unary(
                '/com.aibook.admin.grpc.OtaService/CheckUpdate',
                request_serializer=protos_dot_ota__pb2.CheckUpdateRequest.SerializeToString,
                response_deserializer=protos_dot_ota__pb2.CheckUpdateResponse.FromString,
                _registered_method=True)
        self.ReportUpgradeResult = channel.unary_unary(
                '/com.aibook.admin.grpc.OtaService/ReportUpgradeResult',
                request_serializer=protos_dot_ota__pb2.ReportUpgradeResultRequest.SerializeToString,
                response_deserializer=protos_dot_ota__pb2.ReportUpgradeResultResponse.FromString,
                _registered_method=True)


class OtaServiceServicer(object):
    """OTA服务
    """

    def CheckUpdate(self, request, context):
        """检查更新
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReportUpgradeResult(self, request, context):
        """上报升级结果
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OtaServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CheckUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckUpdate,
                    request_deserializer=protos_dot_ota__pb2.CheckUpdateRequest.FromString,
                    response_serializer=protos_dot_ota__pb2.CheckUpdateResponse.SerializeToString,
            ),
            'ReportUpgradeResult': grpc.unary_unary_rpc_method_handler(
                    servicer.ReportUpgradeResult,
                    request_deserializer=protos_dot_ota__pb2.ReportUpgradeResultRequest.FromString,
                    response_serializer=protos_dot_ota__pb2.ReportUpgradeResultResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'com.aibook.admin.grpc.OtaService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('com.aibook.admin.grpc.OtaService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class OtaService(object):
    """OTA服务
    """

    @staticmethod
    def CheckUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.admin.grpc.OtaService/CheckUpdate',
            protos_dot_ota__pb2.CheckUpdateRequest.SerializeToString,
            protos_dot_ota__pb2.CheckUpdateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReportUpgradeResult(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.admin.grpc.OtaService/ReportUpgradeResult',
            protos_dot_ota__pb2.ReportUpgradeResultRequest.SerializeToString,
            protos_dot_ota__pb2.ReportUpgradeResultResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
