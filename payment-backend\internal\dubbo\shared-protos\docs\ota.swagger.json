{"swagger": "2.0", "info": {"title": "protos/ota.proto", "version": "version not set"}, "tags": [{"name": "OtaService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/admin/check-update": {"get": {"summary": "检查更新", "operationId": "OtaService_CheckUpdate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcCheckUpdateResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "appId", "description": "应用ID", "in": "query", "required": false, "type": "string"}, {"name": "versionName", "description": "当前版本名", "in": "query", "required": false, "type": "string"}, {"name": "versionCode", "description": "当前版本号", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "deviceId", "description": "设备ID", "in": "query", "required": false, "type": "string"}, {"name": "osVersion", "description": "操作系统版本", "in": "query", "required": false, "type": "string"}, {"name": "language", "description": "语言", "in": "query", "required": false, "type": "string"}], "tags": ["OtaService"]}}, "/api/v1/admin/report-upgrade-result": {"post": {"summary": "上报升级结果", "operationId": "OtaService_ReportUpgradeResult", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcReportUpgradeResultResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcReportUpgradeResultRequest"}}], "tags": ["OtaService"]}}}, "definitions": {"grpcCheckUpdateResponse": {"type": "object", "properties": {"hasUpdate": {"type": "boolean", "title": "是否有新版本"}, "isForceUpdate": {"type": "boolean", "title": "是否强制更新"}, "versionName": {"type": "string", "title": "新版本名"}, "versionCode": {"type": "integer", "format": "int32", "title": "新版本号"}, "downloadUrl": {"type": "string", "title": "新版本下载地址"}, "packageSize": {"type": "string", "format": "int64", "title": "安装包大小 (Bytes)"}, "md5": {"type": "string", "title": "安装包MD5"}, "updateLog": {"type": "string", "title": "更新日志"}}, "title": "更新检查响应"}, "grpcReportUpgradeResultRequest": {"type": "object", "properties": {"isSucceed": {"type": "boolean", "title": "是否成功"}, "currentVersionName": {"type": "string", "title": "当前版本名"}, "currentVersionCode": {"type": "integer", "format": "int32", "title": "当前版本号"}, "deviceId": {"type": "string", "title": "设备ID"}}, "title": "上报升级结果请求"}, "grpcReportUpgradeResultResponse": {"type": "object", "properties": {"message": {"type": "string"}}, "title": "上报升级结果响应"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}