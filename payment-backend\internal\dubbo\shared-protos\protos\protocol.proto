/**
  后台管理平台基础服务定义
 */
syntax = "proto3";

package com.aibook.admin.grpc;
option java_multiple_files = true;

option go_package = "./admin;admin";
import "google/api/annotations.proto";

message ProtocolRequest {
  // 国家/地区
  string country = 1;

  // 协议类型
  // 1：用户隐私协议 2：支付协议 3：创作绘本协议
  int32 type = 2;
}

message ProtocolResponse {
  // 协议id
  int32 protocolId = 1;
  // 协议内容，为富文本格式，带段落标识
  string content = 2;

  // 协议版本，一般获取的都是最新版本
  int32 version = 3;
}

message ConfirmProtocolRequest {
  // 用户id
  string userId = 1;
  // 用户邮箱
  string userEmail = 2;
  // 协议id
  string protocolId = 3;
}

message ConfirmProtocolResponse {}



// ProtocolService 用于按照国家/地区和类型获取协议信息。
service ProtocolService {
  rpc getProtocol(ProtocolRequest) returns (ProtocolResponse) {
    option (google.api.http) = {
      get : "/api/v1/admin/protocol"
    };
  }

  // 用户协议确认
  rpc confirmProtocol(ConfirmProtocolRequest) returns (ConfirmProtocolResponse) {
    option (google.api.http) = {
      post: "/api/v1/admin/protocol/confirm",
      body: "*"
    };
  }
}
