package app

import (
	"payment-sync/internal/config"
	"payment-sync/internal/db"
	"payment-sync/internal/domain"
	"payment-sync/internal/repository/mysql"
	"payment-sync/internal/service"

	commonDomain "payment-common/domain"
	commonService "payment-common/service"
	commonDB "payment-common/utils/db"
	"payment-common/logger"

	"go.uber.org/fx"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ConfigModule 配置模块
var ConfigModule = fx.Module("config",
	fx.Provide(func() (*config.Config, error) {
		return config.LoadConfig()
	}),
)

// DatabaseModule 数据库模块
var DatabaseModule = fx.Module("database",
	fx.Provide(func(cfg *config.Config) (*gorm.DB, error) {

		// 初始化数据库连接
		if err := db.Init(&cfg.Database); err != nil {
			return nil, err
		}

		// 执行数据库迁移
		if err := db.Migrate(db.GetDB()); err != nil {
			return nil, err
		}

		return db.GetDB(), nil
	}),
)

// LoggerModule 日志模块
var LoggerModule = fx.Module("logger",
	fx.Provide(func(cfg *config.Config) (*zap.Logger, error) {
		// 根据配置创建logger
		var logger *zap.Logger
		var err error

		if cfg.Log.Format == "console" {
			logger, err = zap.NewDevelopment()
		} else {
			logger, err = zap.NewProduction()
		}

		if err != nil {
			return nil, err
		}

		return logger, nil
	}),
)

// RepositoryModule 仓储模块
var RepositoryModule = fx.Module("repository",
	fx.Provide(
		fx.Annotate(
			mysql.NewOrderRepository,
			fx.As(new(domain.OrderRepository)),
		),
	),
)

// ServiceModule 服务模块
var ServiceModule = fx.Module("service",
	fx.Provide(
		fx.Annotate(
			func(config *config.Config,
				logger *zap.Logger,
				database *gorm.DB,
				orderRepo domain.OrderRepository,
				pointService commonDomain.PointService,
				pointServiceHTTP commonDomain.PointServiceHTTP) domain.SyncService {

				// 初始化全局数据库连接
				commonDB.SetDB(database)

				// 创建履约服务
				fulfillmentService := commonService.NewFulfillmentService(
					orderRepo,
					pointService,
					pointServiceHTTP,
					logger)

				return service.NewSyncService(config, logger, database, orderRepo, fulfillmentService)
			},
			fx.As(new(domain.SyncService)),
		),

		fx.Annotate(
			func(cfg *config.Config, logger logger.Logger) commonDomain.PointService {
				return commonService.NewPointService(cfg, logger.GetZapLogger())
			},
			fx.As(new(commonDomain.PointService)),
		),
		fx.Annotate(
			func(cfg *config.Config, logger logger.Logger) commonDomain.PointServiceHTTP {
				return commonService.NewPointServiceHTTP(cfg, logger.GetZapLogger())
			},
			fx.As(new(commonDomain.PointServiceHTTP)),
		),
	),
)

// AppModule 应用模块
var AppModule = fx.Module("app",
	ConfigModule,
	LoggerModule,
	DatabaseModule,
	RepositoryModule,
	ServiceModule,
)

// NewApp 创建应用
func NewApp() *fx.App {
	return fx.New(
		AppModule,
		fx.NopLogger, // 禁用fx的默认日志，使用我们自己的日志
	)
}
