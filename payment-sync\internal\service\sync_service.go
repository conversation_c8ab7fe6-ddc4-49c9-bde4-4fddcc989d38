package service

import (
	"fmt"
	"time"

	"payment-sync/internal/config"
	"payment-sync/internal/db"
	"payment-sync/internal/domain"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"gorm.io/gorm"

	commonService "payment-common/service"
	"payment-sdk/payment"
	paymentstripe "payment-sdk/payment-stripe"
)

// syncService 订单同步服务实现
type syncService struct {
	config             *config.Config
	logger             *zap.Logger
	db                 *gorm.DB
	orderRepo          domain.OrderRepository
	fulfillmentService commonService.FulfillmentService
	cron               *cron.Cron
}

// NewSyncService 创建同步服务
func NewSyncService(
	config *config.Config,
	logger *zap.Logger,
	database *gorm.DB,
	orderRepo domain.OrderRepository,
	fulfillmentService commonService.FulfillmentService,
) domain.SyncService {
	return &syncService{
		config:             config,
		logger:             logger,
		db:                 database,
		orderRepo:          orderRepo,
		fulfillmentService: fulfillmentService,
		cron:               cron.New(),
	}
}

// Start 启动同步服务
func (s *syncService) Start() error {
	s.logger.Info("Starting sync service", zap.String("cron", s.config.Sync.CronExpression))

	// 添加定时任务
	_, err := s.cron.AddFunc(s.config.Sync.CronExpression, s.syncOrders)
	if err != nil {
		return fmt.Errorf("failed to add cron job: %w", err)
	}

	s.cron.Start()
	s.logger.Info("Sync service started successfully")
	return nil
}

// Stop 停止同步服务
func (s *syncService) Stop() {
	s.logger.Info("Stopping sync service")
	s.cron.Stop()
	s.logger.Info("Sync service stopped")
}

// syncOrders 同步订单的主要逻辑
func (s *syncService) syncOrders() {
	s.logger.Info("Starting order synchronization")

	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -s.config.Sync.TimeRange)

	// 同步付款履约
	if err := s.syncPaymentFulfillment(startTime, endTime); err != nil {
		s.logger.Error("Failed to sync payment fulfillment", zap.Error(err))
	}

	// 同步退款履约
	if err := s.syncRefundFulfillment(startTime, endTime); err != nil {
		s.logger.Error("Failed to sync refund fulfillment", zap.Error(err))
	}

	s.logger.Info("Order synchronization completed")
}

// syncPaymentFulfillment 同步付款履约
func (s *syncService) syncPaymentFulfillment(startTime, endTime time.Time) error {
	s.logger.Info("Syncing payment fulfillment",
		zap.Time("start_time", startTime),
		zap.Time("end_time", endTime))

	// 查询需要同步的订单：pay_status != "fulfilled" 且 refund_status == "none"
	orders, err := s.orderRepo.GetOrdersForPaymentSync(startTime, endTime)
	if err != nil {
		return fmt.Errorf("failed to get orders for payment sync: %w", err)
	}

	s.logger.Info("Found orders for payment sync", zap.Int("count", len(orders)))

	for _, order := range orders {
		if err := s.processPaymentSync(order); err != nil {
			s.logger.Error("Failed to process payment sync",
				zap.String("order_id", order.OrderID),
				zap.Error(err))
			// 记录失败历史
			s.recordSyncHistory(order.OrderID, order.PSPPaymentID, "payment_fulfillment", "failed", err.Error())
		}
	}

	return nil
}

// syncRefundFulfillment 同步退款履约
func (s *syncService) syncRefundFulfillment(startTime, endTime time.Time) error {
	s.logger.Info("Syncing refund fulfillment",
		zap.Time("start_time", startTime),
		zap.Time("end_time", endTime))

	// 查询需要同步的订单：pay_status == "fulfilled" 且 refund_status != "fulfilled"
	orders, err := s.orderRepo.GetOrdersForRefundSync(startTime, endTime)
	if err != nil {
		return fmt.Errorf("failed to get orders for refund sync: %w", err)
	}

	s.logger.Info("Found orders for refund sync", zap.Int("count", len(orders)))

	for _, order := range orders {
		if err := s.processRefundSync(order); err != nil {
			s.logger.Error("Failed to process refund sync",
				zap.String("order_id", order.OrderID),
				zap.Error(err))
			// 记录失败历史
			s.recordSyncHistory(order.OrderID, order.PSPPaymentID, "refund_fulfillment", "failed", err.Error())
		}
	}

	return nil
}

// processPaymentSync 处理单个订单的付款同步
func (s *syncService) processPaymentSync(order *domain.Order) error {
	s.logger.Debug("Processing payment sync", zap.String("order_id", order.OrderID))

	// 调用 payment-sdk 查询支付状态
	paymentSucceeded, _, err := paymentstripe.IsPaymentSucceeded(order.PSPPaymentID)
	if err != nil {
		return fmt.Errorf("failed to check payment status: %w", err)
	}

	s.logger.Debug("Payment status checked",
		zap.String("order_id", order.OrderID),
		zap.Bool("payment_succeeded", paymentSucceeded))

	// 如果付款成功，执行付款履约
	if paymentSucceeded {
		// 先更新支付状态为成功
		params := &payment.EventCallBackParams{
			Event:       "payment.succeeded",
			OrderID:     order.OrderID,
			UserID:      order.UserID,
			PSPEmail:    order.PSPCustomerEmail,
			PSPName:     order.PSPCustomerName,
			PSPCountry:  order.PSPCustomerCountry,
			Country:     order.PSPCustomerCountry,
			TraceID:     fmt.Sprintf("sync-%d", time.Now().Unix()),
			Entitlement: "1", // 这里需要根据实际业务逻辑设置
			Msg:         "Payment sync succeeded",
		}

		err := s.fulfillmentService.UpdatePayStatusSucceeded(params)
		if err != nil {
			return fmt.Errorf("failed to update pay status succeeded: %w", err)
		}

		// 然后执行付款履约
		err = s.fulfillmentService.UpdatePayStatusFulfilled(params)
		if err != nil {
			return fmt.Errorf("failed to update pay status fulfilled: %w", err)
		}

		// 记录成功历史
		s.recordSyncHistory(order.OrderID, order.PSPPaymentID, "payment_fulfillment", "success", "Payment fulfillment completed")
		s.logger.Info("Payment fulfillment completed", zap.String("order_id", order.OrderID))
	}

	return nil
}

// processRefundSync 处理单个订单的退款同步
func (s *syncService) processRefundSync(order *domain.Order) error {
	s.logger.Debug("Processing refund sync", zap.String("order_id", order.OrderID))

	// 调用 payment-sdk 查询支付状态
	_, refundSucceeded, err := paymentstripe.IsPaymentSucceeded(order.PSPPaymentID)
	if err != nil {
		return fmt.Errorf("failed to check payment status: %w", err)
	}

	s.logger.Debug("Refund status checked",
		zap.String("order_id", order.OrderID),
		zap.Bool("refund_succeeded", refundSucceeded))

	// 如果退款成功，执行退款履约
	if refundSucceeded {
		// 先更新退款状态为成功
		params := &payment.EventCallBackParams{
			Event:       "refund.succeeded",
			OrderID:     order.OrderID,
			UserID:      order.UserID,
			PSPEmail:    order.PSPCustomerEmail,
			PSPName:     order.PSPCustomerName,
			PSPCountry:  order.PSPCustomerCountry,
			Country:     order.PSPCustomerCountry,
			TraceID:     fmt.Sprintf("sync-%d", time.Now().Unix()),
			Entitlement: "", // 退款不需要设置权益
			Msg:         "Refund sync succeeded",
		}

		err := s.fulfillmentService.UpdateRefundStatusSucceeded(params)
		if err != nil {
			return fmt.Errorf("failed to update refund status succeeded: %w", err)
		}

		// 然后执行退款履约
		err = s.fulfillmentService.UpdateRefundStatusFulfilled(params)
		if err != nil {
			return fmt.Errorf("failed to update refund status fulfilled: %w", err)
		}

		// 记录成功历史
		s.recordSyncHistory(order.OrderID, order.PSPPaymentID, "refund_fulfillment", "success", "Refund fulfillment completed")
		s.logger.Info("Refund fulfillment completed", zap.String("order_id", order.OrderID))
	}

	return nil
}

// recordSyncHistory 记录同步历史
func (s *syncService) recordSyncHistory(orderID, paymentID, syncType, status, message string) {
	history := &db.SyncHistoryModel{
		OrderID:   orderID,
		SyncType:  syncType,
		Status:    status,
		Message:   message,
		PaymentID: paymentID,
	}

	if err := s.db.Create(history).Error; err != nil {
		s.logger.Error("Failed to record sync history",
			zap.String("order_id", orderID),
			zap.String("sync_type", syncType),
			zap.Error(err))
	} else {
		s.logger.Debug("Sync history recorded",
			zap.String("order_id", orderID),
			zap.String("sync_type", syncType),
			zap.String("status", status))
	}
}
