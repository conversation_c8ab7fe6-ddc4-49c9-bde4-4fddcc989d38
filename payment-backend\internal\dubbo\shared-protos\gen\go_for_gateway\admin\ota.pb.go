//*
// App在线升级(OTA)服务定义

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: protos/ota.proto

package admin

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 更新检查请求
type CheckUpdateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 应用ID
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// 当前版本名
	VersionName string `protobuf:"bytes,2,opt,name=version_name,json=versionName,proto3" json:"version_name,omitempty"`
	// 当前版本号
	VersionCode int32 `protobuf:"varint,3,opt,name=version_code,json=versionCode,proto3" json:"version_code,omitempty"`
	// 设备ID
	DeviceId string `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// 操作系统版本
	OsVersion string `protobuf:"bytes,5,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`
	// 语言
	Language      string `protobuf:"bytes,6,opt,name=language,proto3" json:"language,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUpdateRequest) Reset() {
	*x = CheckUpdateRequest{}
	mi := &file_protos_ota_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpdateRequest) ProtoMessage() {}

func (x *CheckUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_ota_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpdateRequest.ProtoReflect.Descriptor instead.
func (*CheckUpdateRequest) Descriptor() ([]byte, []int) {
	return file_protos_ota_proto_rawDescGZIP(), []int{0}
}

func (x *CheckUpdateRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CheckUpdateRequest) GetVersionName() string {
	if x != nil {
		return x.VersionName
	}
	return ""
}

func (x *CheckUpdateRequest) GetVersionCode() int32 {
	if x != nil {
		return x.VersionCode
	}
	return 0
}

func (x *CheckUpdateRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CheckUpdateRequest) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *CheckUpdateRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

// 更新检查响应
type CheckUpdateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否有新版本
	HasUpdate bool `protobuf:"varint,1,opt,name=has_update,json=hasUpdate,proto3" json:"has_update,omitempty"`
	// 是否强制更新
	IsForceUpdate bool `protobuf:"varint,2,opt,name=is_force_update,json=isForceUpdate,proto3" json:"is_force_update,omitempty"`
	// 新版本名
	VersionName string `protobuf:"bytes,3,opt,name=version_name,json=versionName,proto3" json:"version_name,omitempty"`
	// 新版本号
	VersionCode int32 `protobuf:"varint,4,opt,name=version_code,json=versionCode,proto3" json:"version_code,omitempty"`
	// 新版本下载地址
	DownloadUrl string `protobuf:"bytes,5,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	// 安装包大小 (Bytes)
	PackageSize int64 `protobuf:"varint,6,opt,name=package_size,json=packageSize,proto3" json:"package_size,omitempty"`
	// 安装包MD5
	Md5 string `protobuf:"bytes,7,opt,name=md5,proto3" json:"md5,omitempty"`
	// 更新日志
	UpdateLog     string `protobuf:"bytes,8,opt,name=update_log,json=updateLog,proto3" json:"update_log,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUpdateResponse) Reset() {
	*x = CheckUpdateResponse{}
	mi := &file_protos_ota_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpdateResponse) ProtoMessage() {}

func (x *CheckUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_ota_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpdateResponse.ProtoReflect.Descriptor instead.
func (*CheckUpdateResponse) Descriptor() ([]byte, []int) {
	return file_protos_ota_proto_rawDescGZIP(), []int{1}
}

func (x *CheckUpdateResponse) GetHasUpdate() bool {
	if x != nil {
		return x.HasUpdate
	}
	return false
}

func (x *CheckUpdateResponse) GetIsForceUpdate() bool {
	if x != nil {
		return x.IsForceUpdate
	}
	return false
}

func (x *CheckUpdateResponse) GetVersionName() string {
	if x != nil {
		return x.VersionName
	}
	return ""
}

func (x *CheckUpdateResponse) GetVersionCode() int32 {
	if x != nil {
		return x.VersionCode
	}
	return 0
}

func (x *CheckUpdateResponse) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *CheckUpdateResponse) GetPackageSize() int64 {
	if x != nil {
		return x.PackageSize
	}
	return 0
}

func (x *CheckUpdateResponse) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *CheckUpdateResponse) GetUpdateLog() string {
	if x != nil {
		return x.UpdateLog
	}
	return ""
}

// 上报升级结果请求
type ReportUpgradeResultRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否成功
	IsSucceed bool `protobuf:"varint,1,opt,name=isSucceed,proto3" json:"isSucceed,omitempty"`
	// 当前版本名
	CurrentVersionName string `protobuf:"bytes,2,opt,name=currentVersionName,proto3" json:"currentVersionName,omitempty"`
	// 当前版本号
	CurrentVersionCode int32 `protobuf:"varint,3,opt,name=currentVersionCode,proto3" json:"currentVersionCode,omitempty"`
	// 设备ID
	DeviceId      string `protobuf:"bytes,4,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportUpgradeResultRequest) Reset() {
	*x = ReportUpgradeResultRequest{}
	mi := &file_protos_ota_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportUpgradeResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportUpgradeResultRequest) ProtoMessage() {}

func (x *ReportUpgradeResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_ota_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportUpgradeResultRequest.ProtoReflect.Descriptor instead.
func (*ReportUpgradeResultRequest) Descriptor() ([]byte, []int) {
	return file_protos_ota_proto_rawDescGZIP(), []int{2}
}

func (x *ReportUpgradeResultRequest) GetIsSucceed() bool {
	if x != nil {
		return x.IsSucceed
	}
	return false
}

func (x *ReportUpgradeResultRequest) GetCurrentVersionName() string {
	if x != nil {
		return x.CurrentVersionName
	}
	return ""
}

func (x *ReportUpgradeResultRequest) GetCurrentVersionCode() int32 {
	if x != nil {
		return x.CurrentVersionCode
	}
	return 0
}

func (x *ReportUpgradeResultRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// 上报升级结果响应
type ReportUpgradeResultResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportUpgradeResultResponse) Reset() {
	*x = ReportUpgradeResultResponse{}
	mi := &file_protos_ota_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportUpgradeResultResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportUpgradeResultResponse) ProtoMessage() {}

func (x *ReportUpgradeResultResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_ota_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportUpgradeResultResponse.ProtoReflect.Descriptor instead.
func (*ReportUpgradeResultResponse) Descriptor() ([]byte, []int) {
	return file_protos_ota_proto_rawDescGZIP(), []int{3}
}

func (x *ReportUpgradeResultResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_protos_ota_proto protoreflect.FileDescriptor

const file_protos_ota_proto_rawDesc = "" +
	"\n" +
	"\x10protos/ota.proto\x12\x15com.aibook.admin.grpc\x1a\x1cgoogle/api/annotations.proto\"\xc9\x01\n" +
	"\x12CheckUpdateRequest\x12\x15\n" +
	"\x06app_id\x18\x01 \x01(\tR\x05appId\x12!\n" +
	"\fversion_name\x18\x02 \x01(\tR\vversionName\x12!\n" +
	"\fversion_code\x18\x03 \x01(\x05R\vversionCode\x12\x1b\n" +
	"\tdevice_id\x18\x04 \x01(\tR\bdeviceId\x12\x1d\n" +
	"\n" +
	"os_version\x18\x05 \x01(\tR\tosVersion\x12\x1a\n" +
	"\blanguage\x18\x06 \x01(\tR\blanguage\"\x99\x02\n" +
	"\x13CheckUpdateResponse\x12\x1d\n" +
	"\n" +
	"has_update\x18\x01 \x01(\bR\thasUpdate\x12&\n" +
	"\x0fis_force_update\x18\x02 \x01(\bR\risForceUpdate\x12!\n" +
	"\fversion_name\x18\x03 \x01(\tR\vversionName\x12!\n" +
	"\fversion_code\x18\x04 \x01(\x05R\vversionCode\x12!\n" +
	"\fdownload_url\x18\x05 \x01(\tR\vdownloadUrl\x12!\n" +
	"\fpackage_size\x18\x06 \x01(\x03R\vpackageSize\x12\x10\n" +
	"\x03md5\x18\a \x01(\tR\x03md5\x12\x1d\n" +
	"\n" +
	"update_log\x18\b \x01(\tR\tupdateLog\"\xb6\x01\n" +
	"\x1aReportUpgradeResultRequest\x12\x1c\n" +
	"\tisSucceed\x18\x01 \x01(\bR\tisSucceed\x12.\n" +
	"\x12currentVersionName\x18\x02 \x01(\tR\x12currentVersionName\x12.\n" +
	"\x12currentVersionCode\x18\x03 \x01(\x05R\x12currentVersionCode\x12\x1a\n" +
	"\bdeviceId\x18\x04 \x01(\tR\bdeviceId\"7\n" +
	"\x1bReportUpgradeResultResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage2\xc6\x02\n" +
	"\n" +
	"OtaService\x12\x88\x01\n" +
	"\vCheckUpdate\x12).com.aibook.admin.grpc.CheckUpdateRequest\x1a*.com.aibook.admin.grpc.CheckUpdateResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/api/v1/admin/check-update\x12\xac\x01\n" +
	"\x13ReportUpgradeResult\x121.com.aibook.admin.grpc.ReportUpgradeResultRequest\x1a2.com.aibook.admin.grpc.ReportUpgradeResultResponse\".\x82\xd3\xe4\x93\x02(:\x01*\"#/api/v1/admin/report-upgrade-resultB\x11P\x01Z\r./admin;adminb\x06proto3"

var (
	file_protos_ota_proto_rawDescOnce sync.Once
	file_protos_ota_proto_rawDescData []byte
)

func file_protos_ota_proto_rawDescGZIP() []byte {
	file_protos_ota_proto_rawDescOnce.Do(func() {
		file_protos_ota_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_protos_ota_proto_rawDesc), len(file_protos_ota_proto_rawDesc)))
	})
	return file_protos_ota_proto_rawDescData
}

var file_protos_ota_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_protos_ota_proto_goTypes = []any{
	(*CheckUpdateRequest)(nil),          // 0: com.aibook.admin.grpc.CheckUpdateRequest
	(*CheckUpdateResponse)(nil),         // 1: com.aibook.admin.grpc.CheckUpdateResponse
	(*ReportUpgradeResultRequest)(nil),  // 2: com.aibook.admin.grpc.ReportUpgradeResultRequest
	(*ReportUpgradeResultResponse)(nil), // 3: com.aibook.admin.grpc.ReportUpgradeResultResponse
}
var file_protos_ota_proto_depIdxs = []int32{
	0, // 0: com.aibook.admin.grpc.OtaService.CheckUpdate:input_type -> com.aibook.admin.grpc.CheckUpdateRequest
	2, // 1: com.aibook.admin.grpc.OtaService.ReportUpgradeResult:input_type -> com.aibook.admin.grpc.ReportUpgradeResultRequest
	1, // 2: com.aibook.admin.grpc.OtaService.CheckUpdate:output_type -> com.aibook.admin.grpc.CheckUpdateResponse
	3, // 3: com.aibook.admin.grpc.OtaService.ReportUpgradeResult:output_type -> com.aibook.admin.grpc.ReportUpgradeResultResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_protos_ota_proto_init() }
func file_protos_ota_proto_init() {
	if File_protos_ota_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_protos_ota_proto_rawDesc), len(file_protos_ota_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_ota_proto_goTypes,
		DependencyIndexes: file_protos_ota_proto_depIdxs,
		MessageInfos:      file_protos_ota_proto_msgTypes,
	}.Build()
	File_protos_ota_proto = out.File
	file_protos_ota_proto_goTypes = nil
	file_protos_ota_proto_depIdxs = nil
}
