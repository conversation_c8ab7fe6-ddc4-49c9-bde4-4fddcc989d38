@echo off
setlocal


set PROTO_DIR=../..


set OUT_DIR=gen


if not exist %OUT_DIR% (
    mkdir %OUT_DIR%
)
if not exist %OUT_DIR%\\python (
    mkdir %OUT_DIR%\\python
)
if not exist %OUT_DIR%\\go (
    mkdir %OUT_DIR%\\go
)

if not exist %OUT_DIR%\\go_for_gateway (
    mkdir %OUT_DIR%\\go_for_gateway
)

if not exist ./docs (
    mkdir ./docs
)

cd gen/go

protoc -I=%PROTO_DIR% ^
       -I=%PROTO_DIR%/third_party/googleapis ^
       -I=%PROTO_DIR%/third_party/validate ^
       --go_out=.  ^
       --go-grpc_out=.  ^
       --go-triple_out=.  ^
       --validate_out=lang=go:. ^
       %PROTO_DIR%\protos\market.proto

if %errorlevel% neq 0 (
    echo  %time%  [ERROR] Failed to generate triple code!
    exit /b 1
)else (
    echo  %time%  [OK] Successfully generated triple code
)

cd ../go_for_gateway
protoc -I=%PROTO_DIR% ^
       -I=%PROTO_DIR%/third_party/googleapis ^
       -I=%PROTO_DIR%/third_party/validate ^
       --go_out=.  ^
       --go-grpc_out=.  ^
       --grpc-gateway_out=.  ^
       %PROTO_DIR%\protos\market.proto

if %errorlevel% neq 0 (
    echo  %time%  [ERROR] Failed to generate gateway code!
    exit /b 1
)else (
    echo  %time%  [OK] Successfully generated gateway code
)


cd ../..
set PROTO_DIR=.
protoc  -I=%PROTO_DIR% ^
        -I=%PROTO_DIR%/third_party/googleapis ^
        -I=%PROTO_DIR%/third_party/validate ^
        --openapiv2_out=./docs ^
        --openapiv2_opt logtostderr=true ^
        --openapiv2_opt allow_merge=true ^
        --openapiv2_opt merge_file_name=market ^
        %PROTO_DIR%\protos\market.proto

if %errorlevel% neq 0 (
    echo  %time%  [ERROR] Failed to generate doc code!
    exit /b 1
)else (
    echo  %time%  [OK] Successfully generated doc code
)

endlocal
