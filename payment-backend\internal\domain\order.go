package domain

import (
	"time"

	"payment-backend/internal/middleware"
	commonDomain "payment-common/domain"
)

// 支付方式常量
const (
	PSPProviderStripe = "stripe"
	PSPProviderPayPal = "paypal"
)

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	ProductID   string `json:"product_id" binding:"required" example:"prod_123" validate:"required" comment:"产品ID"`
	ProductDesc string `json:"product_desc" example:"Premium Subscription" comment:"产品描述. 这里存的是产品描述元数据。对应订单中的 ProductDesc 字段。 ProductDesc 存储的是 ProductDescMeta 结构 json 序列化之后再 base64 编码得到的字符串。"`
	Quantity    uint32 `json:"quantity" binding:"required,min=1" example:"1" validate:"required,min=1" comment:"购买数量，最小值为1"`
	Currency    string `json:"currency" example:"USD" comment:"货币代码，如USD、CNY等"`
	PSPProvider string `json:"psp_provider" example:"stripe, paypal" validate:"required" comment:"支付服务提供商"`
}

// CreateOrderResponse 创建订单响应
type CreateOrderResponse struct {
	OrderID     string    `json:"order_id" example:"20250710153045999stripe1234567890123456789" comment:"订单ID"`
	CheckoutURL string    `json:"checkout_url" example:"https://mock-payment.example.com/stripe/checkout/..." comment:"支付链接URL"`
	Amount      float64   `json:"amount" example:"99.99" comment:"订单金额"`
	Currency    string    `json:"currency" example:"USD" comment:"货币代码"`
	ExpiresAt   time.Time `json:"expires_at" example:"2025-07-11T15:30:45Z" comment:"支付链接过期时间"`
}

type RefundOrderRequest struct {
	Amount *float64 `json:"amount,omitempty"`
}

// UpdateOrderRequest 更新订单请求
type UpdateOrderRequest struct {
	PayStatus         string     `json:"pay_status,omitempty"`
	PayedMethod       string     `json:"payed_method,omitempty"`
	CardNumber        string     `json:"card_number,omitempty"`
	PayedAt           *time.Time `json:"payed_at,omitempty"`
	RefundStatus      string     `json:"refund_status,omitempty"`
	RefundedAt        *time.Time `json:"refunded_at,omitempty"`
	PSPPaymentID      string     `json:"psp_payment_id,omitempty"`
	PSPCustomerID     string     `json:"psp_customer_id,omitempty"`
	PSPCustomerEmail  string     `json:"psp_customer_email,omitempty"`
	PSPSubscriptionID string     `json:"psp_subscription_id,omitempty"`
	NetAmount         *float64   `json:"net_amount,omitempty"`
}

// OrderFilter 订单过滤条件
type OrderFilter struct {
	UserID            *string    `json:"user_id,omitempty" form:"user_id" comment:"用户ID"`
	Currency          *string    `json:"currency,omitempty" form:"currency" comment:"货币代码"`
	PayStatus         *string    `json:"pay_status,omitempty" form:"pay_status" comment:"支付状态"`
	PayedMethod       *string    `json:"payed_method,omitempty" form:"payed_method" comment:"支付方式"`
	PSPProvider       *string    `json:"psp_provider,omitempty" form:"psp_provider" comment:"支付服务提供商"`
	PayedAtStart      *time.Time `json:"payed_at_start,omitempty" form:"payed_at_start" comment:"支付时间开始"`
	PayedAtEnd        *time.Time `json:"payed_at_end,omitempty" form:"payed_at_end" comment:"支付时间结束"`
	RefundStatus      *string    `json:"refund_status,omitempty" form:"refund_status" comment:"退款状态"`
	RefundedAtStart   *time.Time `json:"refunded_at_start,omitempty" form:"refunded_at_start" comment:"退款时间开始"`
	RefundedAtEnd     *time.Time `json:"refunded_at_end,omitempty" form:"refunded_at_end" comment:"退款时间结束"`
	PSPPriceID        *string    `json:"psp_price_id,omitempty" form:"psp_price_id" comment:"PSP价格ID"`
	PSPCustomerEmail  *string    `json:"psp_customer_email,omitempty" form:"psp_customer_email" comment:"PSP客户邮箱"`
	PSPSubscriptionID *string    `json:"psp_subscription_id,omitempty" form:"psp_subscription_id" comment:"PSP订阅ID"`
}

// PaginationRequest 分页请求
type PaginationRequest struct {
	Limit  int `json:"limit" form:"limit" binding:"min=1,max=500" comment:"每页数量，最小1，最大500"`
	Offset int `json:"offset" form:"offset" binding:"min=0" comment:"偏移量，从0开始"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Total     int64 `json:"total" comment:"总记录数"`
	Limit     int   `json:"limit" comment:"每页数量"`
	Offset    int   `json:"offset" comment:"偏移量"`
	Remaining int64 `json:"remaining" comment:"剩余记录数"`
}

// ListOrdersResponse 订单列表响应
type ListOrdersResponse struct {
	Orders     []*commonDomain.Order `json:"orders" comment:"订单列表"`
	Pagination *PaginationResponse   `json:"pagination" comment:"分页信息"`
}

// GetUserOrdersResponse 终端用户订单结构
type UserOrderResponse struct {
	OrderID         string     `json:"order_id"`
	ProductID       string     `json:"product_id"`
	ProductDesc     string     `json:"product_desc"`
	ProductSnapshot string     `json:"product_snapshot"`
	Quantity        uint32     `json:"quantity"`
	Amount          float64    `json:"amount"`
	NetAmount       float64    `json:"net_amount"`
	Currency        string     `json:"currency"`
	PayStatus       string     `json:"pay_status"`
	PayedMethod     string     `json:"payed_method"`
	PSPProvider     string     `json:"psp_provider"`
	CardNumber      string     `json:"card_number"`
	PayedAt         *time.Time `json:"payed_at,omitempty"`
	RefundStatus    string     `json:"refund_status"`
	RefundedAt      *time.Time `json:"refunded_at,omitempty"`
}

// GetUserOrdersResponse 订单列表响应
type GetUserOrdersResponse struct {
	UserOrders []*UserOrderResponse `json:"userOrders" comment:"订单列表"`
	Pagination *PaginationResponse  `json:"pagination" comment:"分页信息"`
}

// OrderRepository 订单仓储接口
type OrderRepository interface {
	Create(order *commonDomain.Order) error
	GetByID(id uint64) (*commonDomain.Order, error)
	GetByOrderID(orderID string) (*commonDomain.Order, error)
	GetByOrderIDForUpdate(tx any, orderID string) (*commonDomain.Order, error)
	GetByUserID(userID string, filter *OrderFilter, pagination *PaginationRequest) ([]*commonDomain.Order, int64, error)
	GetByPSPPaymentID(pspPaymentID string) (*commonDomain.Order, error)
	Update(order *commonDomain.Order) error
	UpdateStatus(orderID string, payStatus string) error
	UpdatePaymentIDWithTransaction(tx any, orderID string, paymentID string) error
	SoftDelete(orderID string) error
	List(limit, offset int) ([]*commonDomain.Order, error)
	Count() (int64, error)
	CountByUserID(userID string) (int64, error)
	ListWithFilters(filter *OrderFilter, pagination *PaginationRequest) ([]*commonDomain.Order, int64, error)

	// 事务方法
	CreateWithTransaction(tx any, order *commonDomain.Order) error
	GetByIDWithTransaction(tx any, id uint64) (*commonDomain.Order, error)
	GetByOrderIDWithTransaction(tx any, orderID string) (*commonDomain.Order, error)
	UpdateWithTransaction(tx any, order *commonDomain.Order) error
	UpdateStatusSucceededWithTransaction(tx any,
		orderID string, payStatus string, refundRet string, newSnapshot string) error
	UpdateStatusOtherWithTransaction(tx any, orderID string, payStatus string, refundRet string) error
	UpdateRefundStatusFailedWithTransaction(tx any, orderID string, refundStatus string, refundRet string) error
	UpdateRefundSucceededWithTransaction(tx any, orderID string, refundStatus string, refundRet string) error
	SoftDeleteWithTransaction(tx any, orderID string) error
}

// OrderService 订单服务接口
type OrderService interface {
	CreateOrder(userCtx *middleware.UserContext, requestContext *middleware.RequestContext, req *CreateOrderRequest) (*CreateOrderResponse, error)
	GetOrder(orderID string) (*commonDomain.Order, error)
	GetOrderByID(id uint64) (*commonDomain.Order, error)
	GetUserOrders(userCtx *middleware.UserContext, filter *OrderFilter, pagination *PaginationRequest) (*GetUserOrdersResponse, error)
	ListAllOrders(filter *OrderFilter, pagination *PaginationRequest) (*ListOrdersResponse, error)
	UpdateOrder(orderID string, req *UpdateOrderRequest) error
	ProcessWebhook(provider string, header map[string][]string, payload []byte) error
	CancelOrder(orderID string) error
	RefundOrder(requestContext *middleware.RequestContext, orderID string, amount *float64) error
}

// PaymentGateway 支付网关接口
type PaymentGateway interface {
	CreateCheckoutSession(order *commonDomain.Order, successURL, cancelURL string) (string, error) // 返回checkout URL
	GetPaymentStatus(pspPaymentID string) (string, error)
	RefundPayment(pspPaymentID string, amount float64) error
	VerifyWebhook(payload []byte, signature string) error
}

// NewOrder 创建新的订单实体
func NewOrder(userID string, traceID string, req *CreateOrderRequest, paymentIntentID string) *commonDomain.Order {
	now := time.Now()
	return &commonDomain.Order{
		UserID:             userID,
		TraceID:            traceID,
		ProductID:          req.ProductID,
		ProductDesc:        req.ProductDesc,
		Quantity:           req.Quantity,
		Currency:           req.Currency,
		PayStatus:          commonDomain.PayStatusCreated,
		PayedAt:            &now,
		PSPProvider:        req.PSPProvider,
		RefundStatus:       commonDomain.RefundStatusNone,
		PSPPaymentIntentID: paymentIntentID,
		CreatedAt:          &now,
		UpdatedAt:          &now,
		Deleted:            false,
	}
}
