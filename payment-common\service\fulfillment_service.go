package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"payment-common/domain"
	"payment-common/utils/db"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// FulfillmentService 履约服务接口
type FulfillmentService interface {
	UpdatePayStatusSucceeded(params *domain.EventCallBackParams) error
	UpdatePayStatusFulfilled(params *domain.EventCallBackParams) error
	UpdateRefundStatusSucceeded(params *domain.EventCallBackParams) error
	UpdateRefundStatusFulfilled(params *domain.EventCallBackParams) error
}

// fulfillmentService 履约服务实现
type fulfillmentService struct {
	orderRepo        domain.OrderRepository
	pointService     domain.PointService
	pointServiceHTTP domain.PointServiceHTTP
	logger           *zap.Logger
}

// NewFulfillmentService 创建履约服务
func NewFulfillmentService(
	orderRepo domain.OrderRepository,
	pointService domain.PointService,
	pointServiceHTTP domain.PointServiceHTTP,
	logger *zap.Logger,
) FulfillmentService {
	return &fulfillmentService{
		orderRepo:        orderRepo,
		pointService:     pointService,
		pointServiceHTTP: pointServiceHTTP,
		logger:           logger,
	}
}

// UpdatePayStatusSucceeded 更新支付状态为成功
func (s *fulfillmentService) UpdatePayStatusSucceeded(params *domain.EventCallBackParams) error {
	s.logger.Debug("updatePayStatusSucceeded",
		zap.String("ord.OrderID", params.OrderID))

	if err := db.WithTransaction(func(tx *gorm.DB) error {
		// 先获取订单状态, 避免重复履约.
		ord, err := s.orderRepo.GetByOrderIDForUpdate(tx, params.OrderID)
		if err != nil {
			s.logger.Error("failed to GetByOrderIDForUpdate", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("failed to GetByOrderIDForUpdate(OrderID:%v): %w", params.OrderID, err)
		}
		if ord.PayStatus == domain.PayStatusFulfilled {
			s.logger.Info("already processed Fulfilled before", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("order %v already Fulfilled before", params.OrderID)
		}
		if ord.PayStatus == domain.PayStatusSucceeded {
			s.logger.Info("order status has already been succeeded", zap.String("params.OrderID", params.OrderID))
			return nil
		}

		// 再更新订单状态为付款成功待履约
		now := time.Now()
		ord.PayStatus = domain.PayStatusSucceeded
		ord.PayRet = params.Msg
		ord.PayedAt = &now
		ord.PSPCustomerEmail = params.PSPEmail
		ord.PSPCustomerName = params.PSPName
		ord.PSPCustomerCountry = params.PSPCountry
		err = s.orderRepo.UpdateWithTransaction(tx, ord)

		s.logger.Debug("GetByOrderIDForUpdate",
			zap.String("params.OrderID", params.OrderID),
			zap.String("ord.PayStatus", ord.PayStatus),
			zap.String("ord.PayRet", ord.PayRet),
			zap.String("ord.PSPCustomerEmail", ord.PSPCustomerEmail),
			zap.String("ord.PSPCustomerName", ord.PSPCustomerName),
			zap.String("ord.PSPCustomerCountry", ord.PSPCustomerCountry))

		return err
	}); err != nil {
		return err
	}
	return nil
}

// UpdatePayStatusFulfilled 更新支付状态为已履约
func (s *fulfillmentService) UpdatePayStatusFulfilled(params *domain.EventCallBackParams) error {
	s.logger.Debug("updatePayStatusFulfilled",
		zap.String("ord.OrderID", params.OrderID))

	if err := db.WithTransaction(func(tx *gorm.DB) error {
		// 先获取订单状态, 如果已经履约要避免重复履约.
		ord, err := s.orderRepo.GetByOrderIDForUpdate(tx, params.OrderID)
		if err != nil {
			s.logger.Error("failed to GetByOrderIDForUpdate", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("failed to GetByOrderIDForUpdate(OrderID:%v): %w", params.OrderID, err)
		}
		if ord.PayStatus == domain.PayStatusFulfilled {
			s.logger.Info("already processed Fulfilled before", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("order %v already Fulfilled before", params.OrderID)
		}

		// 履约前先处理参数
		var entitlementInt int64
		if len(params.Entitlement) > 0 {
			v, err := strconv.ParseInt(params.Entitlement, 10, 32)
			if err != nil {
				s.logger.Error("Failed to ParseInt", zap.String("params.OrderID", params.OrderID), zap.Error(err))
				return fmt.Errorf("failed to ParseInt(OrderID:%v): %w", params.OrderID, err)
			}
			entitlementInt = v
		}
		if entitlementInt < 0 {
			s.logger.Error("entitlementInt", zap.Int64("entitlementInt", entitlementInt))
			return fmt.Errorf("entitlementInt is %v, OrderID:%v", entitlementInt, params.OrderID)
		}
		entitlementInt = int64(ord.Quantity) * entitlementInt

		if len(params.PSPEmail) < 1 {
			params.PSPEmail = ord.PSPCustomerEmail
		}
		if len(params.Country) < 1 {
			params.Country = "USA"
		}

		ctx := s.pointService.WithUserContext(context.Background(),
			params.UserID, params.Country, params.TraceID)

		s.logger.Info("pointService.WithUserMetadata",
			zap.Any("ctx", ctx),
			zap.String("params.UserID", params.UserID),
			zap.String("params.Country", params.Country),
			zap.String("params.TraceID", params.TraceID),
			zap.Float32("points", float32(entitlementInt)),
			zap.Uint32("ord.Quantity", ord.Quantity),
			zap.String("params.OrderID", params.OrderID),
		)

		// GRPC 在 userClient, err := userpb.NewUserService(cli) 这一行不定期的 crash. 暂未查原因, 暂时使用 HTTP 方式。
		// err = s.pointService.ChargePoints(ctx, float32(entitlementInt), params.OrderID)
		// if err != nil {
		// 	s.logger.Warn("Failed to pointService.ChargePoints",
		// 		zap.String("params.OrderID", params.OrderID), zap.Error(err))

		// 	// gRPC 失败时调用 HTTP 兜底.
		// 	time.Sleep(time.Second * 1)
		s.pointServiceHTTP.WithUserContext(context.Background(),
			params.UserID, params.Country, params.TraceID)
		err = s.pointServiceHTTP.ChargePoints(ctx, float32(entitlementInt), params.OrderID)
		if err != nil {
			s.logger.Warn("Failed to pointServiceHTTP.ChargePoints",
				zap.String("params.OrderID", params.OrderID), zap.Error(err))
			return fmt.Errorf("failed to pointServiceHTTP.ChargePoints(OrderID:%v): %w", params.OrderID, err)
		}

		s.logger.Debug("s.pointServiceHTTP.ChargePoints Succeeded",
			zap.String("ord.OrderID", params.OrderID),
			zap.Float32("float32(entitlementInt)", float32(entitlementInt)))
		// } else {
		// 	s.logger.Debug("s.pointService.ChargePoints Succeeded",
		// 		zap.String("ord.OrderID", params.OrderID),
		// 		zap.Float32("float32(entitlementInt)", float32(entitlementInt)))
		// }

		// 履约之后再更新订单状态
		newSnapshot := s.updateSnapshotEntitlement(ord, entitlementInt)
		return s.orderRepo.UpdateStatusSucceededWithTransaction(tx,
			params.OrderID, domain.PayStatusFulfilled, params.Msg, newSnapshot)
	},
	); err != nil {
		return err
	}
	return nil
}

// UpdateRefundStatusSucceeded 更新退款状态为成功
func (s *fulfillmentService) UpdateRefundStatusSucceeded(params *domain.EventCallBackParams) error {
	s.logger.Debug("updateRefundStatusSucceeded",
		zap.String("ord.OrderID", params.OrderID))

	if err := db.WithTransaction(func(tx *gorm.DB) error {
		// 先获取订单状态, 避免重复履约.
		ord, err := s.orderRepo.GetByOrderIDForUpdate(tx, params.OrderID)
		if err != nil {
			s.logger.Error("refund, failed to GetByOrderIDForUpdate", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("refund, failed to GetByOrderIDForUpdate(OrderID:%v): %w", params.OrderID, err)
		}
		if ord.RefundStatus == domain.RefundStatusFulfilled {
			s.logger.Info("refund, already processed Fulfilled before", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("refund, order %v already Fulfilled before", params.OrderID)
		}
		if ord.RefundStatus == domain.RefundStatusSucceeded {
			s.logger.Info("refund, order status has already been succeeded", zap.String("params.OrderID", params.OrderID))
			return nil
		}

		// 再更新订单状态为退款成功待履约
		err = s.orderRepo.UpdateRefundSucceededWithTransaction(tx, ord.OrderID,
			domain.RefundStatusSucceeded, params.Msg)

		s.logger.Debug("refund, GetByOrderIDForUpdate",
			zap.String("params.OrderID", params.OrderID),
			zap.String("ord.PayStatus", ord.PayStatus),
			zap.String("ord.PayRet", ord.PayRet),
			zap.String("ord.PSPCustomerEmail", ord.PSPCustomerEmail),
			zap.String("ord.RefundStatus", ord.RefundStatus))

		return err
	}); err != nil {
		return err
	}
	return nil
}

// UpdateRefundStatusFulfilled 更新退款状态为已履约
func (s *fulfillmentService) UpdateRefundStatusFulfilled(params *domain.EventCallBackParams) error {
	s.logger.Debug("updateRefundStatusFulfilled",
		zap.String("ord.OrderID", params.OrderID))

	if err := db.WithTransaction(func(tx *gorm.DB) error {
		// 先获取订单状态, 如果已经履约要避免重复履约.
		ord, err := s.orderRepo.GetByOrderIDForUpdate(tx, params.OrderID)
		if err != nil {
			s.logger.Error("refund, failed to GetByOrderIDForUpdate", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("refund, failed to GetByOrderIDForUpdate(OrderID:%v): %w", params.OrderID, err)
		}
		if ord.RefundStatus == domain.RefundStatusFulfilled {
			s.logger.Info("refund, already processed Fulfilled before", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("refund, order %v already Fulfilled before", params.OrderID)
		}

		// 履约前先处理参数
		var entitlementInt int64
		snap, err := s.getSnapshot(ord)
		if err != nil {
			// TODO： 这里是退款成功了, 但是之前交易快照中找不到当时充值的积分了. 暂时不扣积分了,做退款成功处理.
			s.logger.Error("refund, failed getSnapshot.", zap.Error(err))
		} else {
			s.logger.Debug("getSnapshot return",
				zap.String("ord.OrderID", ord.OrderID),
				zap.Int64("snap.Entitlement", snap.Entitlement))

			entitlementInt = -1 * snap.Entitlement
		}

		if len(params.PSPEmail) < 1 {
			params.PSPEmail = ord.PSPCustomerEmail
		}
		if len(params.Country) < 1 {
			params.Country = ord.PSPCustomerCountry
		}

		ctx := s.pointService.WithUserContext(context.Background(),
			params.UserID, params.Country, params.TraceID)

		s.logger.Info("Refund, pointService.WithUserMetadata",
			zap.Any("ctx", ctx),
			zap.String("params.UserID", params.UserID),
			zap.String("params.Country", params.Country),
			zap.String("params.TraceID", params.TraceID),
			zap.Float32("points", float32(entitlementInt)),
			zap.String("params.OrderID", params.OrderID),
		)

		// 已执行过付款履约，并且积分需要减
		if ord.PayStatus == domain.PayStatusFulfilled && entitlementInt < 0 {

			// GRPC 在 userClient, err := userpb.NewUserService(cli) 这一行不定期的 crash. 暂未查原因, 暂时使用 HTTP 方式。
			// err = s.pointService.ChargePoints(ctx, float32(entitlementInt), params.OrderID)
			// if err != nil {
			// 	s.logger.Warn("Refund, Failed to pointService.ChargePoints", zap.String("params.OrderID", params.OrderID), zap.Error(err))

			// 	// gRPC 失败时调用 HTTP 兜底.
			// 	time.Sleep(time.Second * 1)
			s.pointServiceHTTP.WithUserContext(context.Background(),
				params.UserID, params.Country, params.TraceID)
			err = s.pointServiceHTTP.ChargePoints(ctx, float32(entitlementInt), params.OrderID)
			if err != nil {
				s.logger.Warn("refund, Failed to pointServiceHTTP.ChargePoints", zap.String("params.OrderID", params.OrderID), zap.Error(err))
				return fmt.Errorf("refund, failed to pointServiceHTTP.ChargePoints(OrderID:%v): %w", params.OrderID, err)
			} else {

				s.logger.Debug("refund, s.pointServiceHTTP.ChargePoints Succeeded",
					zap.String("ord.OrderID", params.OrderID),
					zap.Float32("float32(entitlementInt)", float32(entitlementInt)))
			}
			// } else {

			// 	s.logger.Debug("refund, s.pointService.ChargePoints Succeeded",
			// 		zap.String("ord.OrderID", params.OrderID),
			// 		zap.Float32("float32(entitlementInt)", float32(entitlementInt)))
			// }
		}

		// 履约之后再更新订单状态
		return s.orderRepo.UpdateRefundSucceededWithTransaction(tx,
			params.OrderID, domain.RefundStatusFulfilled, params.Msg)
	},
	); err != nil {
		return err
	}
	return nil
}

// getSnapshot 获取产品快照
func (s *fulfillmentService) getSnapshot(ord *domain.Order) (*domain.ProductSnapshot, error) {
	if ord.ProductSnapshot == "" {
		return nil, fmt.Errorf("ProductSnapshot is empty")
	}

	// Base64解码
	decodedBytes, err := base64.StdEncoding.DecodeString(ord.ProductSnapshot)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %w", err)
	}

	// JSON反序列化
	var snapshot domain.ProductSnapshot
	if err := json.Unmarshal(decodedBytes, &snapshot); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return &snapshot, nil
}

// updateSnapshotEntitlement 更新快照中的权益信息
func (s *fulfillmentService) updateSnapshotEntitlement(ord *domain.Order, entitlement int64) string {
	snap, err := s.getSnapshot(ord)
	if err != nil {
		s.logger.Error("Failed to get snapshot", zap.Error(err))
		return ord.ProductSnapshot
	}

	snap.Entitlement = entitlement

	// 序列化为JSON
	jsonBytes, err := json.Marshal(snap)
	if err != nil {
		s.logger.Error("Failed to marshal snapshot", zap.Error(err))
		return ord.ProductSnapshot
	}

	// Base64编码
	return base64.StdEncoding.EncodeToString(jsonBytes)
}
