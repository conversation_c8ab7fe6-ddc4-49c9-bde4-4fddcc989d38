apiVersion: apps/v1
kind: StatefulSet
metadata:
  namespace: dev2
  name: payment-service
  labels:
    app: payment-service
spec:
  serviceName: payment-service-headless
  replicas: 1
  selector:
    matchLabels:
      app: payment-service
  template:
    metadata:
      labels:
        app: payment-service
    spec:
      containers:
        - name: payment-service-container
          image: <DOCKER_BUILD_IMAGE_SERVER1>
          imagePullPolicy: IfNotPresent 
          ports:
            - containerPort: 8080
          envFrom:
            - configMapRef:
                name: payment-service-configmap
          env:
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 10
            failureThreshold: 3
            successThreshold: 1
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 20
            periodSeconds: 10
            failureThreshold: 3
            successThreshold: 1
          resources:
            requests:
              memory: "500M"
              cpu: "100m"
            limits:
              memory: "1024M"
              cpu: "500m"

---
apiVersion: v1
kind: Service
metadata:
  namespace: dev2
  name: payment-service-svc
  labels:
    app: payment-service
spec:
  ports:
  - name: http
    nodePort: 25906
    port: 8080
    protocol: TCP
    targetPort: 8080
  - name: grpc
    nodePort: 25907
    port: 20000
    protocol: TCP
    targetPort: 20000
  type: NodePort
  selector:
    app: payment-service
