package payment

// 创建产品和价格
type CreateOnetimeProductAndPriceReq struct {
	ProductName        string `json:"product_name"`
	ProductDescription string `json:"product_description"`
	Currency           string `json:"currency"`
	UnitAmount         int64  `json:"unit_amount"`
}

type CreateOnetimeProductAndPriceRsp struct {
	ProductID string `json:"product_id"`
	PriceID   string `json:"price_id"`
}

type UpdateOnetimeProductAndPriceReq struct {
	ProductID          string `json:"product_id"`
	ProductName        string `json:"product_name"`
	ProductDescription string `json:"product_description"`
	PriceID            string `json:"price_id"`
	Currency           string `json:"currency"`
	UnitAmount         int64  `json:"unit_amount"`
	LookupKey          string `json:"lookupKey"`
}

type UpdateOnetimeProductAndPriceRsp struct {
	ProductID       string `json:"product_id"`
	PriceIDRefeshed bool   `json:"price_id_refreshed"`
	PriceID         string `json:"price_id"`
}

// Order 订单实体
type CheckoutReq struct {
	OrderID     string `json:"order_id"`
	UserID      string `json:"user_id"`
	TraceID     string `json:"trace_id"`
	ProductID   string `json:"product_id"`
	PriceID     string `json:"price_id"`
	Quantity    uint32 `json:"quantity"`
	Country     string `json:"country"`
	Entitlement string `json:"entitlement"` // 权益
	SuccessURL  string `json:"success_url"`
	CancelURL   string `json:"cancel_url"`
}

type CreateCheckoutRsp struct {
	CheckoutURL string `json:"checkout_url"`
	PaymentID   string `json:"payment_id"`
}

type GatewayConfig struct {
	SecretKey        string `json:"secret_key"`
	WebhookSecretKey string `json:"webhook_secret_key"`
}

// EventCallBackParams 事件回调参数
type EventCallBackParams struct {
	Event       string `json:"event"`
	OrderID     string `json:"order_id"`
	UserID      string `json:"user_id"`
	PSPEmail    string `json:"psp_email"`
	PSPName     string `json:"psp_name"`
	PSPCountry  string `json:"psp_country"`
	Country     string `json:"country"`
	TraceID     string `json:"trace_id"`
	Entitlement string `json:"entitlement"`
	Msg         string `json:"msg"`
}

const (
	PaymentEventPending   = "payment.paid_pending"   // 待支付
	PaymentEventSucceeded = "payment.succeeded"      // 支付成功
	PaymentEventCanceled  = "payment.canceled"       // 支付失败
	PaymentEventFailed    = "payment.payment_failed" // 支付过期
	PaymentEventExpired   = "checkout.expired"       // 取消支付

	RefundEventSucceeded = "refund.succeeded" // 退款成功
	RefundEventFailed    = "refund.failed"    // 退款失败

	EventOthers = "payment.others" // 其他事件, 可以用于日志记录.
)

// PaymentGateway 支付网关接口
type PaymentGateway interface {
	CreateOnetimeProductAndPrice(req *CreateOnetimeProductAndPriceReq) (*CreateOnetimeProductAndPriceRsp, error)
	UpdateProductAndPrice(req *UpdateOnetimeProductAndPriceReq) (*UpdateOnetimeProductAndPriceRsp, error)
	DeleteProductAndDisablePrice(productID, priceID string) error
	CreateCheckout(req *CheckoutReq) (*CreateCheckoutRsp, error) // 返回checkout URL
	IsPaymentSucceeded(paymentID string) (bool, bool, error)     // 返回 付款成功, 退款成功, error
	RefundPayment(orderID string, traceID string, pspPaymentID string, pspPaymentIntentID string, amount float64) (string, error)
	Webhook(header map[string][]string, payload []byte, cb func(*EventCallBackParams) error) error
}
