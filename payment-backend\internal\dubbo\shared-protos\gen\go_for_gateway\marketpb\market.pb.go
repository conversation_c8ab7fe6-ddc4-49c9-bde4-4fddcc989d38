// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v6.31.1
// source: protos/market.proto

package marketpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *UserIdRequest) Reset() {
	*x = UserIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdRequest) ProtoMessage() {}

func (x *UserIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdRequest.ProtoReflect.Descriptor instead.
func (*UserIdRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{0}
}

func (x *UserIdRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type BookIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookId uint64 `protobuf:"varint,1,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`
}

func (x *BookIdRequest) Reset() {
	*x = BookIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookIdRequest) ProtoMessage() {}

func (x *BookIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookIdRequest.ProtoReflect.Descriptor instead.
func (*BookIdRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{1}
}

func (x *BookIdRequest) GetBookId() uint64 {
	if x != nil {
		return x.BookId
	}
	return 0
}

type UserIdPageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page   uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"` //页码
	Size   uint32 `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"` //每页数量
}

func (x *UserIdPageRequest) Reset() {
	*x = UserIdPageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdPageRequest) ProtoMessage() {}

func (x *UserIdPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdPageRequest.ProtoReflect.Descriptor instead.
func (*UserIdPageRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{2}
}

func (x *UserIdPageRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserIdPageRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *UserIdPageRequest) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type SearchBookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchStr string `protobuf:"bytes,1,opt,name=search_str,json=searchStr,proto3" json:"search_str,omitempty"`
	Page      uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"` //页码
	Size      uint32 `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"` //每页数量
}

func (x *SearchBookRequest) Reset() {
	*x = SearchBookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchBookRequest) ProtoMessage() {}

func (x *SearchBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchBookRequest.ProtoReflect.Descriptor instead.
func (*SearchBookRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{3}
}

func (x *SearchBookRequest) GetSearchStr() string {
	if x != nil {
		return x.SearchStr
	}
	return ""
}

func (x *SearchBookRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchBookRequest) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type AdminSearchBookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page                uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                      //页码
	Size                uint32 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`                      //每页数量
	OrderBy             uint32 `protobuf:"varint,3,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"` // 排序字段( 0-国家、1-主题、2-状态、3-年龄段、4-名称筛选)
	IsDesc              bool   `protobuf:"varint,4,opt,name=is_desc,json=isDesc,proto3" json:"is_desc,omitempty"`    // 是否降序
	SearchByTitle       string `protobuf:"bytes,5,opt,name=search_by_title,json=searchByTitle,proto3" json:"search_by_title,omitempty"`
	SearchStatus        string `protobuf:"bytes,6,opt,name=search_status,json=searchStatus,proto3" json:"search_status,omitempty"`
	SearchByDescription string `protobuf:"bytes,7,opt,name=search_by_description,json=searchByDescription,proto3" json:"search_by_description,omitempty"`
	SearchByAgeRange    string `protobuf:"bytes,8,opt,name=search_by_age_range,json=searchByAgeRange,proto3" json:"search_by_age_range,omitempty"`
	SearchByCountry     string `protobuf:"bytes,9,opt,name=search_by_country,json=searchByCountry,proto3" json:"search_by_country,omitempty"`
	SearchByRecommend   string `protobuf:"bytes,10,opt,name=search_by_recommend,json=searchByRecommend,proto3" json:"search_by_recommend,omitempty"` // 是否推荐(yes or no)
}

func (x *AdminSearchBookRequest) Reset() {
	*x = AdminSearchBookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminSearchBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminSearchBookRequest) ProtoMessage() {}

func (x *AdminSearchBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminSearchBookRequest.ProtoReflect.Descriptor instead.
func (*AdminSearchBookRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{4}
}

func (x *AdminSearchBookRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *AdminSearchBookRequest) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *AdminSearchBookRequest) GetOrderBy() uint32 {
	if x != nil {
		return x.OrderBy
	}
	return 0
}

func (x *AdminSearchBookRequest) GetIsDesc() bool {
	if x != nil {
		return x.IsDesc
	}
	return false
}

func (x *AdminSearchBookRequest) GetSearchByTitle() string {
	if x != nil {
		return x.SearchByTitle
	}
	return ""
}

func (x *AdminSearchBookRequest) GetSearchStatus() string {
	if x != nil {
		return x.SearchStatus
	}
	return ""
}

func (x *AdminSearchBookRequest) GetSearchByDescription() string {
	if x != nil {
		return x.SearchByDescription
	}
	return ""
}

func (x *AdminSearchBookRequest) GetSearchByAgeRange() string {
	if x != nil {
		return x.SearchByAgeRange
	}
	return ""
}

func (x *AdminSearchBookRequest) GetSearchByCountry() string {
	if x != nil {
		return x.SearchByCountry
	}
	return ""
}

func (x *AdminSearchBookRequest) GetSearchByRecommend() string {
	if x != nil {
		return x.SearchByRecommend
	}
	return ""
}

type BookListPageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Books []*BookInfo `protobuf:"bytes,1,rep,name=books,proto3" json:"books,omitempty"`
	Page  uint32      `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`   //页码
	Size  uint32      `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`   //每页数量
	Total uint32      `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"` // 总数
}

func (x *BookListPageResponse) Reset() {
	*x = BookListPageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookListPageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookListPageResponse) ProtoMessage() {}

func (x *BookListPageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookListPageResponse.ProtoReflect.Descriptor instead.
func (*BookListPageResponse) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{5}
}

func (x *BookListPageResponse) GetBooks() []*BookInfo {
	if x != nil {
		return x.Books
	}
	return nil
}

func (x *BookListPageResponse) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BookListPageResponse) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *BookListPageResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type BookList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Books []*BookInfo `protobuf:"bytes,1,rep,name=books,proto3" json:"books,omitempty"`
}

func (x *BookList) Reset() {
	*x = BookList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookList) ProtoMessage() {}

func (x *BookList) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookList.ProtoReflect.Descriptor instead.
func (*BookList) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{6}
}

func (x *BookList) GetBooks() []*BookInfo {
	if x != nil {
		return x.Books
	}
	return nil
}

type OperateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Operater      string `protobuf:"bytes,1,opt,name=operater,proto3" json:"operater,omitempty"`                                   // 操作者
	Identity      string `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`                                   // 身份
	OperateType   string `protobuf:"bytes,3,opt,name=operate_type,json=operateType,proto3" json:"operate_type,omitempty"`          // 操作类型(审核、发布、下架、删除等)
	OperateDesc   string `protobuf:"bytes,4,opt,name=operate_desc,json=operateDesc,proto3" json:"operate_desc,omitempty"`          // 操作描述或结论(审批未通过的原因等)
	OperateBookId uint64 `protobuf:"varint,6,opt,name=operate_book_id,json=operateBookId,proto3" json:"operate_book_id,omitempty"` // 操作的绘本
	CreatedAt     uint32 `protobuf:"varint,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`               // 操作时间
}

func (x *OperateInfo) Reset() {
	*x = OperateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateInfo) ProtoMessage() {}

func (x *OperateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateInfo.ProtoReflect.Descriptor instead.
func (*OperateInfo) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{7}
}

func (x *OperateInfo) GetOperater() string {
	if x != nil {
		return x.Operater
	}
	return ""
}

func (x *OperateInfo) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *OperateInfo) GetOperateType() string {
	if x != nil {
		return x.OperateType
	}
	return ""
}

func (x *OperateInfo) GetOperateDesc() string {
	if x != nil {
		return x.OperateDesc
	}
	return ""
}

func (x *OperateInfo) GetOperateBookId() uint64 {
	if x != nil {
		return x.OperateBookId
	}
	return 0
}

func (x *OperateInfo) GetCreatedAt() uint32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type BookOptLogsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Logs  []*OperateInfo `protobuf:"bytes,1,rep,name=logs,proto3" json:"logs,omitempty"`
	Page  uint32         `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`   //页码
	Size  uint32         `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`   //每页数量
	Total uint32         `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"` // 总数
}

func (x *BookOptLogsResponse) Reset() {
	*x = BookOptLogsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookOptLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookOptLogsResponse) ProtoMessage() {}

func (x *BookOptLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookOptLogsResponse.ProtoReflect.Descriptor instead.
func (*BookOptLogsResponse) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{8}
}

func (x *BookOptLogsResponse) GetLogs() []*OperateInfo {
	if x != nil {
		return x.Logs
	}
	return nil
}

func (x *BookOptLogsResponse) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BookOptLogsResponse) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *BookOptLogsResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type BookDownloadRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookId    uint64 `protobuf:"varint,1,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`
	UserId    uint64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CreatedAt uint32 `protobuf:"varint,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Title     string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"` // 绘本标题
}

func (x *BookDownloadRecord) Reset() {
	*x = BookDownloadRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookDownloadRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookDownloadRecord) ProtoMessage() {}

func (x *BookDownloadRecord) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookDownloadRecord.ProtoReflect.Descriptor instead.
func (*BookDownloadRecord) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{9}
}

func (x *BookDownloadRecord) GetBookId() uint64 {
	if x != nil {
		return x.BookId
	}
	return 0
}

func (x *BookDownloadRecord) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BookDownloadRecord) GetCreatedAt() uint32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *BookDownloadRecord) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type BookDownloadRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*BookDownloadRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	Page    uint32                `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`   //页码
	Size    uint32                `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`   //每页数量
	Total   uint32                `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"` // 总数
}

func (x *BookDownloadRecordResponse) Reset() {
	*x = BookDownloadRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookDownloadRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookDownloadRecordResponse) ProtoMessage() {}

func (x *BookDownloadRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookDownloadRecordResponse.ProtoReflect.Descriptor instead.
func (*BookDownloadRecordResponse) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{10}
}

func (x *BookDownloadRecordResponse) GetRecords() []*BookDownloadRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *BookDownloadRecordResponse) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BookDownloadRecordResponse) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *BookDownloadRecordResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type BookModifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookId      uint64       `protobuf:"varint,1,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`               // 绘本ID
	ThemeIds    []uint32     `protobuf:"varint,2,rep,packed,name=theme_ids,json=themeIds,proto3" json:"theme_ids,omitempty"`  // 绘本主题, 空表示不修改
	Status      string       `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`                              // 绘本状态, 空表示不修改
	RecommendAt string       `protobuf:"bytes,4,opt,name=recommend_at,json=recommendAt,proto3" json:"recommend_at,omitempty"` // 推荐时间, 空表示不修改， 0-表示不推荐，秒级时间戳-表示推荐
	OperateInfo *OperateInfo `protobuf:"bytes,5,opt,name=operate_info,json=operateInfo,proto3" json:"operate_info,omitempty"` // 操作信息
}

func (x *BookModifyRequest) Reset() {
	*x = BookModifyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookModifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookModifyRequest) ProtoMessage() {}

func (x *BookModifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookModifyRequest.ProtoReflect.Descriptor instead.
func (*BookModifyRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{11}
}

func (x *BookModifyRequest) GetBookId() uint64 {
	if x != nil {
		return x.BookId
	}
	return 0
}

func (x *BookModifyRequest) GetThemeIds() []uint32 {
	if x != nil {
		return x.ThemeIds
	}
	return nil
}

func (x *BookModifyRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *BookModifyRequest) GetRecommendAt() string {
	if x != nil {
		return x.RecommendAt
	}
	return ""
}

func (x *BookModifyRequest) GetOperateInfo() *OperateInfo {
	if x != nil {
		return x.OperateInfo
	}
	return nil
}

type BookId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookId uint64 `protobuf:"varint,1,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`
}

func (x *BookId) Reset() {
	*x = BookId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookId) ProtoMessage() {}

func (x *BookId) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookId.ProtoReflect.Descriptor instead.
func (*BookId) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{12}
}

func (x *BookId) GetBookId() uint64 {
	if x != nil {
		return x.BookId
	}
	return 0
}

type BookIdPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookId uint64 `protobuf:"varint,1,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`
	Page   uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"` //页码
	Size   uint32 `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"` //每页数量
}

func (x *BookIdPage) Reset() {
	*x = BookIdPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookIdPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookIdPage) ProtoMessage() {}

func (x *BookIdPage) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookIdPage.ProtoReflect.Descriptor instead.
func (*BookIdPage) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{13}
}

func (x *BookIdPage) GetBookId() uint64 {
	if x != nil {
		return x.BookId
	}
	return 0
}

func (x *BookIdPage) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BookIdPage) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ThemeBooksPageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ThemeId uint32 `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"` // 绘本主题ID
	Page    uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                      //页码
	Size    uint32 `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`                      //每页数量
	Total   uint32 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`                    // 总数
}

func (x *ThemeBooksPageRequest) Reset() {
	*x = ThemeBooksPageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThemeBooksPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThemeBooksPageRequest) ProtoMessage() {}

func (x *ThemeBooksPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThemeBooksPageRequest.ProtoReflect.Descriptor instead.
func (*ThemeBooksPageRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{14}
}

func (x *ThemeBooksPageRequest) GetThemeId() uint32 {
	if x != nil {
		return x.ThemeId
	}
	return 0
}

func (x *ThemeBooksPageRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ThemeBooksPageRequest) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ThemeBooksPageRequest) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type BookInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookId        uint64   `protobuf:"varint,1,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`                      // 绘本ID
	Country       string   `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`                                   // 绘本国家
	Lang          string   `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`                                         // 绘本语言
	Title         string   `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`                                       // 绘本标题
	Description   string   `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                           // 绘本描述
	Cover         string   `protobuf:"bytes,6,opt,name=cover,proto3" json:"cover,omitempty"`                                       // 绘本封面
	Type          string   `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`                                         // 绘本类型：图文、动态
	AgeRange      string   `protobuf:"bytes,8,opt,name=age_range,json=ageRange,proto3" json:"age_range,omitempty"`                 // 绘本年龄段
	RecommendedAt uint32   `protobuf:"varint,9,opt,name=recommended_at,json=recommendedAt,proto3" json:"recommended_at,omitempty"` // 绘本推荐时间, 值为0表示不推荐
	Downloads     uint32   `protobuf:"varint,10,opt,name=downloads,proto3" json:"downloads,omitempty"`                             // 绘本下载次数
	FromUserId    uint64   `protobuf:"varint,11,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`       // 绘本作者ID
	Status        string   `protobuf:"bytes,12,opt,name=status,proto3" json:"status,omitempty"`                                    // 绘本状态
	Themes        []string `protobuf:"bytes,13,rep,name=themes,proto3" json:"themes,omitempty"`                                    // 绘本主题
	CreatedAt     uint32   `protobuf:"varint,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`            // 绘本进入绘本市场时间
	UpdatedAt     uint32   `protobuf:"varint,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`            // 最后更新时间
}

func (x *BookInfo) Reset() {
	*x = BookInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookInfo) ProtoMessage() {}

func (x *BookInfo) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookInfo.ProtoReflect.Descriptor instead.
func (*BookInfo) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{15}
}

func (x *BookInfo) GetBookId() uint64 {
	if x != nil {
		return x.BookId
	}
	return 0
}

func (x *BookInfo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *BookInfo) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *BookInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BookInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BookInfo) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *BookInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BookInfo) GetAgeRange() string {
	if x != nil {
		return x.AgeRange
	}
	return ""
}

func (x *BookInfo) GetRecommendedAt() uint32 {
	if x != nil {
		return x.RecommendedAt
	}
	return 0
}

func (x *BookInfo) GetDownloads() uint32 {
	if x != nil {
		return x.Downloads
	}
	return 0
}

func (x *BookInfo) GetFromUserId() uint64 {
	if x != nil {
		return x.FromUserId
	}
	return 0
}

func (x *BookInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *BookInfo) GetThemes() []string {
	if x != nil {
		return x.Themes
	}
	return nil
}

func (x *BookInfo) GetCreatedAt() uint32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *BookInfo) GetUpdatedAt() uint32 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type ThemeBookTopkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Books []*BookInfo `protobuf:"bytes,1,rep,name=books,proto3" json:"books,omitempty"` // 绘本数组
}

func (x *ThemeBookTopkResponse) Reset() {
	*x = ThemeBookTopkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThemeBookTopkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThemeBookTopkResponse) ProtoMessage() {}

func (x *ThemeBookTopkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThemeBookTopkResponse.ProtoReflect.Descriptor instead.
func (*ThemeBookTopkResponse) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{16}
}

func (x *ThemeBookTopkResponse) GetBooks() []*BookInfo {
	if x != nil {
		return x.Books
	}
	return nil
}

type ThemeListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Themes []*Theme `protobuf:"bytes,1,rep,name=themes,proto3" json:"themes,omitempty"` // 绘本主题数组
}

func (x *ThemeListResponse) Reset() {
	*x = ThemeListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThemeListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThemeListResponse) ProtoMessage() {}

func (x *ThemeListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThemeListResponse.ProtoReflect.Descriptor instead.
func (*ThemeListResponse) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{17}
}

func (x *ThemeListResponse) GetThemes() []*Theme {
	if x != nil {
		return x.Themes
	}
	return nil
}

type Theme struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ThemeId        uint32 `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`                      // 绘本主题ID
	Name           string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                            // 绘本主题名称
	Description    string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`                              // 绘本主题描述
	Cover          string `protobuf:"bytes,4,opt,name=cover,proto3" json:"cover,omitempty"`                                          // 绘本主题封面
	RecommendScore uint32 `protobuf:"varint,5,opt,name=recommend_score,json=recommendScore,proto3" json:"recommend_score,omitempty"` // 绘本主题推荐分数
	BookCount      uint32 `protobuf:"varint,6,opt,name=book_count,json=bookCount,proto3" json:"book_count,omitempty"`                // 绘本数量
}

func (x *Theme) Reset() {
	*x = Theme{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Theme) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Theme) ProtoMessage() {}

func (x *Theme) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Theme.ProtoReflect.Descriptor instead.
func (*Theme) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{18}
}

func (x *Theme) GetThemeId() uint32 {
	if x != nil {
		return x.ThemeId
	}
	return 0
}

func (x *Theme) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Theme) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Theme) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *Theme) GetRecommendScore() uint32 {
	if x != nil {
		return x.RecommendScore
	}
	return 0
}

func (x *Theme) GetBookCount() uint32 {
	if x != nil {
		return x.BookCount
	}
	return 0
}

type ThemeIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ThemeId uint32 `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
}

func (x *ThemeIdRequest) Reset() {
	*x = ThemeIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThemeIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThemeIdRequest) ProtoMessage() {}

func (x *ThemeIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThemeIdRequest.ProtoReflect.Descriptor instead.
func (*ThemeIdRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{19}
}

func (x *ThemeIdRequest) GetThemeId() uint32 {
	if x != nil {
		return x.ThemeId
	}
	return 0
}

type ThemeDetailListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Themes []*Theme `protobuf:"bytes,1,rep,name=themes,proto3" json:"themes,omitempty"` // 绘本主题数组
	Page   uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`    //页码
	Size   uint32   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`    //每页数量
	Total  uint32   `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`  // 总数
}

func (x *ThemeDetailListResponse) Reset() {
	*x = ThemeDetailListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThemeDetailListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThemeDetailListResponse) ProtoMessage() {}

func (x *ThemeDetailListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThemeDetailListResponse.ProtoReflect.Descriptor instead.
func (*ThemeDetailListResponse) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{20}
}

func (x *ThemeDetailListResponse) GetThemes() []*Theme {
	if x != nil {
		return x.Themes
	}
	return nil
}

func (x *ThemeDetailListResponse) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ThemeDetailListResponse) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ThemeDetailListResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{21}
}

type ShareBookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookId uint64 `protobuf:"varint,1,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"` // 绘本ID
}

func (x *ShareBookRequest) Reset() {
	*x = ShareBookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShareBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareBookRequest) ProtoMessage() {}

func (x *ShareBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareBookRequest.ProtoReflect.Descriptor instead.
func (*ShareBookRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{22}
}

func (x *ShareBookRequest) GetBookId() uint64 {
	if x != nil {
		return x.BookId
	}
	return 0
}

var File_protos_market_proto protoreflect.FileDescriptor

var file_protos_market_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f,
	0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x31, 0x0a, 0x0d, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x31,
	0x0a, 0x0d, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x20, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x6b, 0x49,
	0x64, 0x22, 0x6f, 0x0a, 0x11, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x50, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x22, 0x6c, 0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x22, 0x92, 0x03, 0x0a, 0x16, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02,
	0x28, 0x01, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x69, 0x73, 0x44, 0x65, 0x73, 0x63, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x79, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x5f, 0x62, 0x79, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x79, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x13, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42,
	0x79, 0x41, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x62, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x79, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f,
	0x62, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x79, 0x52, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x22, 0x9e, 0x01, 0x0a, 0x14, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36,
	0x0a, 0x05, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x05, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x42, 0x0a, 0x08, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x36, 0x0a, 0x05, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x05, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x22, 0xd2, 0x01, 0x0a, 0x0b, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x63, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0x9e, 0x01, 0x0a, 0x13, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x70, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x73,
	0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0x8d, 0x01, 0x0a, 0x12, 0x42, 0x6f, 0x6f, 0x6b, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28,
	0x01, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32,
	0x02, 0x28, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x22, 0xb2, 0x01, 0x0a, 0x1a, 0x42, 0x6f, 0x6f, 0x6b, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x44, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xd5, 0x01, 0x0a, 0x11, 0x42, 0x6f, 0x6f, 0x6b, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x62,
	0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d,
	0x52, 0x08, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x41, 0x74, 0x12, 0x46, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e,
	0x67, 0x72, 0x70, 0x63, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2a, 0x0a,
	0x06, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28,
	0x01, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x22, 0x68, 0x0a, 0x0a, 0x42, 0x6f, 0x6f,
	0x6b, 0x49, 0x64, 0x50, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28,
	0x01, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x15, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x73, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a,
	0x08, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x07, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x22, 0xa5, 0x03, 0x0a, 0x08, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17,
	0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x62, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x67, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x66, 0x72, 0x6f,
	0x6d, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x4f, 0x0a, 0x15, 0x54, 0x68, 0x65,
	0x6d, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x54, 0x6f, 0x70, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x36, 0x0a, 0x05, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x05, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x22, 0x4a, 0x0a, 0x11, 0x54, 0x68,
	0x65, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x35, 0x0a, 0x06, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x06,
	0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x22, 0xb6, 0x01, 0x0a, 0x05, 0x54, 0x68, 0x65, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x62, 0x6f, 0x6f, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x34, 0x0a, 0x0e, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x22, 0x0a, 0x08, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x07, 0x74, 0x68,
	0x65, 0x6d, 0x65, 0x49, 0x64, 0x22, 0xa0, 0x01, 0x0a, 0x17, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x35, 0x0a, 0x06, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x54, 0x68, 0x65, 0x6d, 0x65,
	0x52, 0x06, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x34, 0x0a, 0x10, 0x53, 0x68, 0x61, 0x72, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52,
	0x06, 0x62, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x32, 0xf1, 0x0d, 0x0a, 0x0d, 0x4d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x75, 0x0a, 0x09, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x28, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62,
	0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x68, 0x61, 0x72, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x73, 0x68, 0x61, 0x72, 0x65,
	0x12, 0x75, 0x0a, 0x0a, 0x42, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1e,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x1a, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x2f, 0x7b, 0x62,
	0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x80, 0x01, 0x0a, 0x0a, 0x42, 0x6f, 0x6f, 0x6b,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x12, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62,
	0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x42, 0x6f, 0x6f, 0x6b, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x62, 0x6f, 0x6f, 0x6b,
	0x2f, 0x7b, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x84, 0x01, 0x0a, 0x0b, 0x42,
	0x6f, 0x6f, 0x6b, 0x4f, 0x70, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67,
	0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x50, 0x61, 0x67, 0x65, 0x1a, 0x2b,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x70, 0x74, 0x4c,
	0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x2f, 0x6f, 0x70, 0x74, 0x2f, 0x6c, 0x6f, 0x67,
	0x73, 0x12, 0x84, 0x01, 0x0a, 0x0a, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b,
	0x12, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e,
	0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x17, 0x12, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x94, 0x01, 0x0a, 0x0f, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x2e, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12,
	0x78, 0x0a, 0x09, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x54, 0x6f, 0x70, 0x6b, 0x12, 0x1d, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x29, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e,
	0x67, 0x72, 0x70, 0x63, 0x2e, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x74,
	0x68, 0x65, 0x6d, 0x65, 0x2f, 0x74, 0x6f, 0x70, 0x6b, 0x12, 0x81, 0x01, 0x0a, 0x0d, 0x54, 0x68,
	0x65, 0x6d, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x54, 0x6f, 0x70, 0x6b, 0x12, 0x26, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e,
	0x67, 0x72, 0x70, 0x63, 0x2e, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b,
	0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f,
	0x6b, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x74, 0x68,
	0x65, 0x6d, 0x65, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x2f, 0x74, 0x6f, 0x70, 0x6b, 0x12, 0x90, 0x01,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x73, 0x12,
	0x2d, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x73, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x22, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x2f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x73,
	0x12, 0x81, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x2f,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x7c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x42, 0x6f, 0x6f, 0x6b, 0x73, 0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72,
	0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61,
	0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x20, 0x12, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2f, 0x62, 0x6f, 0x6f,
	0x6b, 0x73, 0x12, 0xa3, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x29, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69,
	0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63,
	0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x25, 0x12, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x2f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x90, 0x01, 0x0a, 0x16, 0x53, 0x79, 0x6e,
	0x63, 0x42, 0x6f, 0x6f, 0x6b, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b,
	0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x6f, 0x6f,
	0x6b, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x67,
	0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2a, 0x22, 0x28, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x2f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x2f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x42, 0x15, 0x5a, 0x13, 0x2e,
	0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x70, 0x62, 0x3b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_market_proto_rawDescOnce sync.Once
	file_protos_market_proto_rawDescData = file_protos_market_proto_rawDesc
)

func file_protos_market_proto_rawDescGZIP() []byte {
	file_protos_market_proto_rawDescOnce.Do(func() {
		file_protos_market_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_market_proto_rawDescData)
	})
	return file_protos_market_proto_rawDescData
}

var file_protos_market_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_protos_market_proto_goTypes = []interface{}{
	(*UserIdRequest)(nil),              // 0: com.aibook.market.grpc.UserIdRequest
	(*BookIdRequest)(nil),              // 1: com.aibook.market.grpc.BookIdRequest
	(*UserIdPageRequest)(nil),          // 2: com.aibook.market.grpc.UserIdPageRequest
	(*SearchBookRequest)(nil),          // 3: com.aibook.market.grpc.SearchBookRequest
	(*AdminSearchBookRequest)(nil),     // 4: com.aibook.market.grpc.AdminSearchBookRequest
	(*BookListPageResponse)(nil),       // 5: com.aibook.market.grpc.BookListPageResponse
	(*BookList)(nil),                   // 6: com.aibook.market.grpc.BookList
	(*OperateInfo)(nil),                // 7: com.aibook.market.grpc.OperateInfo
	(*BookOptLogsResponse)(nil),        // 8: com.aibook.market.grpc.BookOptLogsResponse
	(*BookDownloadRecord)(nil),         // 9: com.aibook.market.grpc.BookDownloadRecord
	(*BookDownloadRecordResponse)(nil), // 10: com.aibook.market.grpc.BookDownloadRecordResponse
	(*BookModifyRequest)(nil),          // 11: com.aibook.market.grpc.BookModifyRequest
	(*BookId)(nil),                     // 12: com.aibook.market.grpc.BookId
	(*BookIdPage)(nil),                 // 13: com.aibook.market.grpc.BookIdPage
	(*ThemeBooksPageRequest)(nil),      // 14: com.aibook.market.grpc.ThemeBooksPageRequest
	(*BookInfo)(nil),                   // 15: com.aibook.market.grpc.BookInfo
	(*ThemeBookTopkResponse)(nil),      // 16: com.aibook.market.grpc.ThemeBookTopkResponse
	(*ThemeListResponse)(nil),          // 17: com.aibook.market.grpc.ThemeListResponse
	(*Theme)(nil),                      // 18: com.aibook.market.grpc.Theme
	(*ThemeIdRequest)(nil),             // 19: com.aibook.market.grpc.ThemeIdRequest
	(*ThemeDetailListResponse)(nil),    // 20: com.aibook.market.grpc.ThemeDetailListResponse
	(*Empty)(nil),                      // 21: com.aibook.market.grpc.Empty
	(*ShareBookRequest)(nil),           // 22: com.aibook.market.grpc.ShareBookRequest
}
var file_protos_market_proto_depIdxs = []int32{
	15, // 0: com.aibook.market.grpc.BookListPageResponse.books:type_name -> com.aibook.market.grpc.BookInfo
	15, // 1: com.aibook.market.grpc.BookList.books:type_name -> com.aibook.market.grpc.BookInfo
	7,  // 2: com.aibook.market.grpc.BookOptLogsResponse.logs:type_name -> com.aibook.market.grpc.OperateInfo
	9,  // 3: com.aibook.market.grpc.BookDownloadRecordResponse.records:type_name -> com.aibook.market.grpc.BookDownloadRecord
	7,  // 4: com.aibook.market.grpc.BookModifyRequest.operate_info:type_name -> com.aibook.market.grpc.OperateInfo
	15, // 5: com.aibook.market.grpc.ThemeBookTopkResponse.books:type_name -> com.aibook.market.grpc.BookInfo
	18, // 6: com.aibook.market.grpc.ThemeListResponse.themes:type_name -> com.aibook.market.grpc.Theme
	18, // 7: com.aibook.market.grpc.ThemeDetailListResponse.themes:type_name -> com.aibook.market.grpc.Theme
	22, // 8: com.aibook.market.grpc.MarketService.ShareBook:input_type -> com.aibook.market.grpc.ShareBookRequest
	12, // 9: com.aibook.market.grpc.MarketService.BookDetail:input_type -> com.aibook.market.grpc.BookId
	11, // 10: com.aibook.market.grpc.MarketService.BookModify:input_type -> com.aibook.market.grpc.BookModifyRequest
	13, // 11: com.aibook.market.grpc.MarketService.BookOptLogs:input_type -> com.aibook.market.grpc.BookIdPage
	3,  // 12: com.aibook.market.grpc.MarketService.SearchBook:input_type -> com.aibook.market.grpc.SearchBookRequest
	4,  // 13: com.aibook.market.grpc.MarketService.AdminSearchBook:input_type -> com.aibook.market.grpc.AdminSearchBookRequest
	21, // 14: com.aibook.market.grpc.MarketService.ThemeTopk:input_type -> com.aibook.market.grpc.Empty
	19, // 15: com.aibook.market.grpc.MarketService.ThemeBookTopk:input_type -> com.aibook.market.grpc.ThemeIdRequest
	14, // 16: com.aibook.market.grpc.MarketService.GetThemeBooks:input_type -> com.aibook.market.grpc.ThemeBooksPageRequest
	21, // 17: com.aibook.market.grpc.MarketService.GetThemeList:input_type -> com.aibook.market.grpc.Empty
	21, // 18: com.aibook.market.grpc.MarketService.GetRecommendBooks:input_type -> com.aibook.market.grpc.Empty
	2,  // 19: com.aibook.market.grpc.MarketService.GetBookDownloadRecord:input_type -> com.aibook.market.grpc.UserIdPageRequest
	1,  // 20: com.aibook.market.grpc.MarketService.SyncBookDownloadRecord:input_type -> com.aibook.market.grpc.BookIdRequest
	21, // 21: com.aibook.market.grpc.MarketService.ShareBook:output_type -> com.aibook.market.grpc.Empty
	15, // 22: com.aibook.market.grpc.MarketService.BookDetail:output_type -> com.aibook.market.grpc.BookInfo
	21, // 23: com.aibook.market.grpc.MarketService.BookModify:output_type -> com.aibook.market.grpc.Empty
	8,  // 24: com.aibook.market.grpc.MarketService.BookOptLogs:output_type -> com.aibook.market.grpc.BookOptLogsResponse
	5,  // 25: com.aibook.market.grpc.MarketService.SearchBook:output_type -> com.aibook.market.grpc.BookListPageResponse
	5,  // 26: com.aibook.market.grpc.MarketService.AdminSearchBook:output_type -> com.aibook.market.grpc.BookListPageResponse
	17, // 27: com.aibook.market.grpc.MarketService.ThemeTopk:output_type -> com.aibook.market.grpc.ThemeListResponse
	6,  // 28: com.aibook.market.grpc.MarketService.ThemeBookTopk:output_type -> com.aibook.market.grpc.BookList
	5,  // 29: com.aibook.market.grpc.MarketService.GetThemeBooks:output_type -> com.aibook.market.grpc.BookListPageResponse
	20, // 30: com.aibook.market.grpc.MarketService.GetThemeList:output_type -> com.aibook.market.grpc.ThemeDetailListResponse
	6,  // 31: com.aibook.market.grpc.MarketService.GetRecommendBooks:output_type -> com.aibook.market.grpc.BookList
	10, // 32: com.aibook.market.grpc.MarketService.GetBookDownloadRecord:output_type -> com.aibook.market.grpc.BookDownloadRecordResponse
	21, // 33: com.aibook.market.grpc.MarketService.SyncBookDownloadRecord:output_type -> com.aibook.market.grpc.Empty
	21, // [21:34] is the sub-list for method output_type
	8,  // [8:21] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_protos_market_proto_init() }
func file_protos_market_proto_init() {
	if File_protos_market_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_market_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdPageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchBookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminSearchBookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookListPageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookOptLogsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookDownloadRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookDownloadRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookModifyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookIdPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThemeBooksPageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThemeBookTopkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThemeListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Theme); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThemeIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThemeDetailListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShareBookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_market_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_market_proto_goTypes,
		DependencyIndexes: file_protos_market_proto_depIdxs,
		MessageInfos:      file_protos_market_proto_msgTypes,
	}.Build()
	File_protos_market_proto = out.File
	file_protos_market_proto_rawDesc = nil
	file_protos_market_proto_goTypes = nil
	file_protos_market_proto_depIdxs = nil
}
