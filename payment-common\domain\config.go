package domain

// NacosConfig Nacos配置（统一配置中心和注册中心）
type NacosConfig struct {
	Enabled   bool     `mapstructure:"enabled"`   // 是否启用Nacos
	Endpoints []string `mapstructure:"endpoints"` // Nacos服务器地址列表
	Namespace string   `mapstructure:"namespace"` // 命名空间
	Username  string   `mapstructure:"username"`  // 用户名
	Password  string   `mapstructure:"password"`  // 密码

	// 配置中心相关
	Config NacosConfigCenter `mapstructure:"config"` // 配置中心设置

	// 注册中心相关
	Registry NacosRegistry `mapstructure:"registry"` // 注册中心设置
}

// NacosConfigCenter Nacos配置中心配置
type NacosConfigCenter struct {
	Enabled bool   `mapstructure:"enabled"` // 是否启用配置中心
	DataID  string `mapstructure:"data_id"` // 配置文件ID
	Group   string `mapstructure:"group"`   // 配置组
	Timeout int    `mapstructure:"timeout"` // 连接超时时间(秒)
}

// NacosRegistry Nacos注册中心配置
type NacosRegistry struct {
	Enabled bool `mapstructure:"enabled"` // 是否启用注册中心
}
