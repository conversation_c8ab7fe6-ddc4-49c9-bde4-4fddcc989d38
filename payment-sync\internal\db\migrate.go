package db

import (
	"fmt"

	"gorm.io/gorm"
)

// Migrate 执行数据库迁移
func Migrate(db *gorm.DB) error {
	// 自动迁移表结构（这会自动创建模型中定义的基础索引）
	if err := AutoMigrate(db); err != nil {
		return fmt.Errorf("failed to auto migrate: %w", err)
	}

	return nil
}

// IndexDefinition 索引定义结构
type IndexDefinition struct {
	Name    string   // 索引名称
	Table   string   // 表名
	Columns []string // 列名列表
	Unique  bool     // 是否唯一索引
	Comment string   // 索引注释
}

// createIndex 创建单个索引
func createIndex(db *gorm.DB, idx IndexDefinition) error {
	indexType := "INDEX"
	if idx.Unique {
		indexType = "UNIQUE INDEX"
	}

	columnsStr := fmt.Sprintf("(%s)", fmt.Sprintf("%s", fmt.Sprintf("%v", idx.Columns)[1:len(fmt.Sprintf("%v", idx.Columns))-1]))
	columnsStr = fmt.Sprintf("(%s)", joinColumns(idx.Columns))

	sql := fmt.Sprintf("CREATE %s IF NOT EXISTS %s ON %s %s",
		indexType, idx.Name, idx.Table, columnsStr)

	return db.Exec(sql).Error
}

// joinColumns 连接列名
func joinColumns(columns []string) string {
	if len(columns) == 0 {
		return ""
	}
	if len(columns) == 1 {
		return columns[0]
	}

	result := columns[0]
	for i := 1; i < len(columns); i++ {
		result += ", " + columns[i]
	}
	return result
}

// DropTables 删除所有表（用于测试或重置）
func DropTables(db *gorm.DB) error {
	// 删除订单表
	tables := []string{
		"orders",
	}

	for _, table := range tables {
		if err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s", table)).Error; err != nil {
			return fmt.Errorf("failed to drop table %s: %w", table, err)
		}
	}

	return nil
}
