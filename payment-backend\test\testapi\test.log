2025/08/01 17:58:53 === Test Session Started at 2025-08-01 17:58:53 ===
2025/08/01 17:58:53 +++++++++++++++ Starting test for dev1 +++++++++++++++
2025/08/01 17:58:53 Testing environment: dev1
2025/08/01 17:58:53 External URL: http://ny10wt9045294.vicp.fun:25639
2025/08/01 17:58:53 Local Gin URL: http://192.168.1.200:15445
2025/08/01 17:58:53 Local RPC URL: http://192.168.1.200:15446
2025/08/01 17:58:53 
2025/08/01 17:58:53 === Getting External API Token ===
2025/08/01 17:58:54 Token obtained successfully: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMzM4OTQ0NzQ5Njc3MzE0MDQ4IiwiY291bnRyeSI6IkNOIiwiYWdlX3JhbmdlIjoiOCsiLCJsYW5ndWFnZSI6IkNITiIsImlzcyI6InNlY3JldCIsImV4cCI6MTc1NDEyODczNCwiaWF0IjoxNzU0MDQyMzM0fQ.wFZOCWc7T3hscqDogkL5BedkixeXOJS0PhjLE8IRQAc
2025/08/01 17:58:54 --- Admin Add Packages (Internal Dubbo RPC) ---
2025/08/01 17:58:54 URL: http://192.168.1.200:15446/com.aibook.storepb.grpc.StoreService/AdminAddPackages
2025/08/01 17:58:54 Headers: map[x-trace-id:test-1754042334151758100-1754042334]
2025/08/01 17:58:54 Request Body: {PackageName:10积分特惠流量包 PackageDesc:[test_api]RPC API测试 PackageType:traffic Entitlement:10 EntitlementDesc:10个积分 OriginalPrice:8.88 DiscountPrice:0xc000186110 DiscountStartTime:<nil> DiscountEndTime:<nil> DiscountDesc:圣诞节限时特别优惠 SaleStatus:on_sale Currency:USD Country:USA PSPProductID: PSPProductDesc: PSPPriceID: PSPPriceDesc: ProductImg: Extra1: Extra2: Extra3:0 Extra4:0}
2025/08/01 17:58:55 Response Status: 200
2025/08/01 17:58:55 Response Body: {
      "package_id": "3f254e0e-36f0-49a8-b565-9c975364766d",
      "package_name": "10积分特惠流量包",
      "package_desc": "[test_api]RPC API测试",
      "package_type": "traffic",
      "entitlement": 10,
      "entitlement_desc": "10个积分",
      "original_price": 8.88,
      "discount_price": 6.66,
      "discount_desc": "圣诞节限时特别优惠",
      "sale_status": "on_sale",
      "currency": "USD",
      "country": "USA",
      "psp_product_id": "prod_SmpOYq0LDJfHDL",
      "psp_price_id": "price_1RrFjnC53MAl6WmqTcqVMvG3",
      "created_at": "2025-08-01T09:58:55.335219742Z",
      "updated_at": "2025-08-01T09:58:55.335219742Z"
    }
2025/08/01 17:58:55 --- Admin Update Packages (Internal Dubbo RPC) ---
2025/08/01 17:58:55 URL: http://192.168.1.200:15446/com.aibook.storepb.grpc.StoreService/AdminUpdatePackages
2025/08/01 17:58:55 Headers: map[x-trace-id:test-1754042335333148800-1754042335]
2025/08/01 17:58:55 Request Body: {PackageID:3f254e0e-36f0-49a8-b565-9c975364766d PackageName:0xc000194240 PackageDesc:<nil> Entitlement:<nil> EntitlementDesc:<nil> OriginalPrice:0xc0001866b0 DiscountPrice:<nil> DiscountStartTime:<nil> DiscountEndTime:<nil> DiscountDesc:<nil> SaleStatus:<nil> Currency:<nil> Country:<nil> PSPProductID:<nil> PSPProductDesc:<nil> PSPPriceID:<nil> PSPPriceDesc:<nil> ProductImg:<nil> Extra1:<nil> Extra2:<nil> Extra3:<nil> Extra4:<nil>}
2025/08/01 17:58:56 Response Status: 200
2025/08/01 17:58:56 Response Body: {
      "message": "Package updated successfully"
    }
2025/08/01 17:58:56 --- List All Packages (External Gin HTTP) ---
2025/08/01 17:58:56 URL: http://ny10wt9045294.vicp.fun:25639/api/v1/pay-service/store-service/packages?limit=50&offset=0
2025/08/01 17:58:56 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMzM4OTQ0NzQ5Njc3MzE0MDQ4IiwiY291bnRyeSI6IkNOIiwiYWdlX3JhbmdlIjoiOCsiLCJsYW5ndWFnZSI6IkNITiIsImlzcyI6InNlY3JldCIsImV4cCI6MTc1NDEyODczNCwiaWF0IjoxNzU0MDQyMzM0fQ.wFZOCWc7T3hscqDogkL5BedkixeXOJS0PhjLE8IRQAc x-trace-id:test-1754042336589814300-1754042336]
2025/08/01 17:58:56 Response Status: 200
2025/08/01 17:58:56 Response Body: {
      "packages": [
        {
          "package_id": "3f254e0e-36f0-49a8-b565-9c975364766d",
          "package_name": "RPC更新后的套餐",
          "package_desc": "[test_api]RPC API测试",
          "package_type": "traffic",
          "entitlement": 10,
          "entitlement_desc": "10个积分",
          "price": 6.66,
          "original_price": 9.99,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "圣诞节限时特别优惠",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "USA",
          "psp_product_id": "prod_SmpOYq0LDJfHDL",
          "psp_product_desc": "",
          "psp_price_id": "price_1RrFjnC53MAl6WmqPlEsP0pz",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "be64ab23-a8cf-4666-9ee4-c974529c524b",
          "package_name": "RPC更新后的套餐",
          "package_desc": "[test_api]RPC API测试",
          "package_type": "traffic",
          "entitlement": 10,
          "entitlement_desc": "10个积分",
          "price": 6.66,
          "original_price": 9.99,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "圣诞节限时特别优惠",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "USA",
          "psp_product_id": "prod_SmpKdhdcYCTpkK",
          "psp_product_desc": "",
          "psp_price_id": "price_1RrFfOC53MAl6WmqXrsjJTq2",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "b59659ba-cb8c-4014-86a3-d68759f7c99f",
          "package_name": "RPC更新后的套餐",
          "package_desc": "[test_api]RPC API测试",
          "package_type": "traffic",
          "entitlement": 10,
          "entitlement_desc": "10个积分",
          "price": 6.66,
          "original_price": 9.99,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "圣诞节限时特别优惠",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "USA",
          "psp_product_id": "prod_SmpCcakp3RgwCx",
          "psp_product_desc": "",
          "psp_price_id": "price_1RrFXqC53MAl6Wmql0XAy0mp",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "1157d3b1-298f-41e0-ba40-f54ecae80840",
          "package_name": "RPC更新后的套餐",
          "package_desc": "[test_api]RPC API测试",
          "package_type": "traffic",
          "entitlement": 10,
          "entitlement_desc": "10个积分",
          "price": 6.66,
          "original_price": 9.99,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "圣诞节限时特别优惠",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "USA",
          "psp_product_id": "prod_Smp6IZuzhhmiuH",
          "psp_product_desc": "",
          "psp_price_id": "price_1RrFSUC53MAl6Wmqly8M5M2Y",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "8906fb0b-02fc-4b3b-9f91-2075e710c8bf",
          "package_name": "RPC更新后的套餐",
          "package_desc": "[test_api]RPC API测试",
          "package_type": "traffic",
          "entitlement": 10,
          "entitlement_desc": "10个积分",
          "price": 6.66,
          "original_price": 9.99,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "圣诞节限时特别优惠",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "USA",
          "psp_product_id": "prod_SmoYflnBVT9NBR",
          "psp_product_desc": "",
          "psp_price_id": "price_1RrEuwC53MAl6WmqqXFnOD65",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "345a9646-e4d8-43eb-bed4-8392a6d13944",
          "package_name": "RPC更新后的套餐",
          "package_desc": "[test_api]RPC API测试",
          "package_type": "traffic",
          "entitlement": 10,
          "entitlement_desc": "10个积分",
          "price": 6.66,
          "original_price": 9.99,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "圣诞节限时特别优惠",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "USA",
          "psp_product_id": "prod_Smme4woAqoflr4",
          "psp_product_desc": "",
          "psp_price_id": "price_1RrD4nC53MAl6Wmq8BCWZRNW",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "653ea944-cb0f-42c1-951d-f9ef6fbc4332",
          "package_name": "RPC更新后的套餐",
          "package_desc": "[test_api]RPC API测试",
          "package_type": "traffic",
          "entitlement": 10,
          "entitlement_desc": "10个积分",
          "price": 6.66,
          "original_price": 9.99,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "圣诞节限时特别优惠",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "USA",
          "psp_product_id": "prod_Smlkger7bvnjO3",
          "psp_product_desc": "",
          "psp_price_id": "price_1RrCDSC53MAl6WmqSBhvGGA2",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "645fbd1d-2f3f-416b-85d5-23e54d2d9bd6",
          "package_name": "RPC更新后的套餐",
          "package_desc": "[test_api]RPC API测试",
          "package_type": "traffic",
          "entitlement": 10,
          "entitlement_desc": "10个积分",
          "price": 6.66,
          "original_price": 9.99,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "圣诞节限时特别优惠",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "USA",
          "psp_product_id": "prod_SmlUvJmXTF7LDI",
          "psp_product_desc": "",
          "psp_price_id": "price_1RrBxYC53MAl6Wmq8KjU88F0",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "aa7c5db5-a9cd-4108-acfb-592476f1eb2a",
          "package_name": "RPC更新后的套餐",
          "package_desc": "[test_api]RPC API测试",
          "package_type": "traffic",
          "entitlement": 10,
          "entitlement_desc": "10个积分",
          "price": 6.66,
          "original_price": 9.99,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "圣诞节限时特别优惠",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "USA",
          "psp_product_id": "prod_Sml95NjGPc84sM",
          "psp_product_desc": "",
          "psp_price_id": "price_1RrBcvC53MAl6WmqxpRN0ug5",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "ea64f212-68b3-417d-93ce-dfe70b9b2c72",
          "package_name": "RPC更新后的套餐",
          "package_desc": "[test_api]RPC API测试",
          "package_type": "traffic",
          "entitlement": 10,
          "entitlement_desc": "10个积分",
          "price": 6.66,
          "original_price": 9.99,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "圣诞节限时特别优惠",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "USA",
          "psp_product_id": "prod_Smgigi2fBU5Lsq",
          "psp_product_desc": "",
          "psp_price_id": "price_1Rr7LXC53MAl6Wmqvs9dftnZ",
          "psp_price_desc": "",
          "product_img": ""
        },
        {
          "package_id": "fb540673-97a7-41ca-833b-fb7ad3d29ba1",
          "package_name": "RPC更新后的套餐",
          "package_desc": "[test_api]RPC API测试",
          "package_type": "traffic",
          "entitlement": 10,
          "entitlement_desc": "10个积分",
          "price": 6.66,
          "original_price": 9.99,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "圣诞节限时特别优惠",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "USA",
          "psp_product_id": "prod_SmP1KzMzGIwBT1",
          "psp_product_desc": "",
          "psp_price_id": "price_1RqqDAC53MAl6WmquKS2ZvPF",
          "psp_price_desc": "",
          "product_img": ""
        }
      ],
      "pagination": {
        "total": 11,
        "limit": 50,
        "offset": 0,
        "remaining": 0
      }
    }
2025/08/01 17:58:56 --------------- Finished test for dev1 ---------------
2025/08/01 17:58:56 === Starting Test Summary ===


##################### EACH TEST SUMMARY #####################

=== Test Summary for dev1 ===
✅ PASS Get External Token (0.00s) - Token obtained successfully
✅ PASS Admin Add Packages (Internal Dubbo RPC) (1.18s) - Package created successfully via Dubbo RPC
✅ PASS Admin Update Packages (Internal Dubbo RPC) (1.26s) - Package updated successfully via Dubbo RPC
✅ PASS List All Packages (External Gin HTTP) (0.06s) - Packages retrieved successfully via External Gin HTTP

Results: 4/4 tests passed
🎉 All tests passed!

2025/08/01 17:58:56 === Test Session Ended at 2025-08-01 17:58:56 ===
