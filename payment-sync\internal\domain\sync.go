package domain

import (
	"time"

	commonDomain "payment-common/domain"
)

// Order 订单实体 - 使用 payment-common 中的定义
type Order = commonDomain.Order

// OrderRepository 订单仓储接口
type OrderRepository interface {
	GetByOrderID(orderID string) (*Order, error)
	GetByOrderIDForUpdate(tx any, orderID string) (*Order, error)
	UpdateWithTransaction(tx any, order *Order) error
	UpdateStatusSucceededWithTransaction(tx any, orderID string, payStatus string, refundRet string, newSnapshot string) error
	UpdateRefundSucceededWithTransaction(tx any, orderID string, refundStatus string, refundRet string) error
	GetOrdersForPaymentSync(startTime, endTime time.Time) ([]*Order, error)
	GetOrdersForRefundSync(startTime, endTime time.Time) ([]*Order, error)
}

// SyncService 订单同步服务接口
type SyncService interface {
	Start() error
	Stop()
}
