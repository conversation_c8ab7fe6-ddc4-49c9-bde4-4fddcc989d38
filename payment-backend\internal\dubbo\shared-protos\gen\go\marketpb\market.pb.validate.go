// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: protos/market.proto

package marketpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UserIdRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserIdRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserIdRequestMultiError, or
// nil if none found.
func (m *UserIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UserIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() < 1 {
		err := UserIdRequestValidationError{
			field:  "UserId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UserIdRequestMultiError(errors)
	}

	return nil
}

// UserIdRequestMultiError is an error wrapping multiple validation errors
// returned by UserIdRequest.ValidateAll() if the designated constraints
// aren't met.
type UserIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserIdRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserIdRequestMultiError) AllErrors() []error { return m }

// UserIdRequestValidationError is the validation error returned by
// UserIdRequest.Validate if the designated constraints aren't met.
type UserIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserIdRequestValidationError) ErrorName() string { return "UserIdRequestValidationError" }

// Error satisfies the builtin error interface
func (e UserIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserIdRequestValidationError{}

// Validate checks the field values on BookIdRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BookIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BookIdRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BookIdRequestMultiError, or
// nil if none found.
func (m *BookIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BookIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetBookId() < 1 {
		err := BookIdRequestValidationError{
			field:  "BookId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BookIdRequestMultiError(errors)
	}

	return nil
}

// BookIdRequestMultiError is an error wrapping multiple validation errors
// returned by BookIdRequest.ValidateAll() if the designated constraints
// aren't met.
type BookIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BookIdRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BookIdRequestMultiError) AllErrors() []error { return m }

// BookIdRequestValidationError is the validation error returned by
// BookIdRequest.Validate if the designated constraints aren't met.
type BookIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BookIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BookIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BookIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BookIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BookIdRequestValidationError) ErrorName() string { return "BookIdRequestValidationError" }

// Error satisfies the builtin error interface
func (e BookIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBookIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BookIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BookIdRequestValidationError{}

// Validate checks the field values on UserIdPageRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UserIdPageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserIdPageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserIdPageRequestMultiError, or nil if none found.
func (m *UserIdPageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UserIdPageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() < 1 {
		err := UserIdPageRequestValidationError{
			field:  "UserId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPage() < 1 {
		err := UserIdPageRequestValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := UserIdPageRequestValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UserIdPageRequestMultiError(errors)
	}

	return nil
}

// UserIdPageRequestMultiError is an error wrapping multiple validation errors
// returned by UserIdPageRequest.ValidateAll() if the designated constraints
// aren't met.
type UserIdPageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserIdPageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserIdPageRequestMultiError) AllErrors() []error { return m }

// UserIdPageRequestValidationError is the validation error returned by
// UserIdPageRequest.Validate if the designated constraints aren't met.
type UserIdPageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserIdPageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserIdPageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserIdPageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserIdPageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserIdPageRequestValidationError) ErrorName() string {
	return "UserIdPageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UserIdPageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserIdPageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserIdPageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserIdPageRequestValidationError{}

// Validate checks the field values on SearchBookRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchBookRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchBookRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchBookRequestMultiError, or nil if none found.
func (m *SearchBookRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchBookRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SearchStr

	if m.GetPage() < 1 {
		err := SearchBookRequestValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := SearchBookRequestValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SearchBookRequestMultiError(errors)
	}

	return nil
}

// SearchBookRequestMultiError is an error wrapping multiple validation errors
// returned by SearchBookRequest.ValidateAll() if the designated constraints
// aren't met.
type SearchBookRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchBookRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchBookRequestMultiError) AllErrors() []error { return m }

// SearchBookRequestValidationError is the validation error returned by
// SearchBookRequest.Validate if the designated constraints aren't met.
type SearchBookRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchBookRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchBookRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchBookRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchBookRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchBookRequestValidationError) ErrorName() string {
	return "SearchBookRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SearchBookRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchBookRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchBookRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchBookRequestValidationError{}

// Validate checks the field values on AdminSearchBookRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdminSearchBookRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdminSearchBookRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdminSearchBookRequestMultiError, or nil if none found.
func (m *AdminSearchBookRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AdminSearchBookRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPage() < 1 {
		err := AdminSearchBookRequestValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := AdminSearchBookRequestValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrderBy

	// no validation rules for IsDesc

	// no validation rules for SearchByTitle

	// no validation rules for SearchStatus

	// no validation rules for SearchByDescription

	// no validation rules for SearchByAgeRange

	// no validation rules for SearchByCountry

	// no validation rules for SearchByRecommend

	if len(errors) > 0 {
		return AdminSearchBookRequestMultiError(errors)
	}

	return nil
}

// AdminSearchBookRequestMultiError is an error wrapping multiple validation
// errors returned by AdminSearchBookRequest.ValidateAll() if the designated
// constraints aren't met.
type AdminSearchBookRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdminSearchBookRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdminSearchBookRequestMultiError) AllErrors() []error { return m }

// AdminSearchBookRequestValidationError is the validation error returned by
// AdminSearchBookRequest.Validate if the designated constraints aren't met.
type AdminSearchBookRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdminSearchBookRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdminSearchBookRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdminSearchBookRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdminSearchBookRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdminSearchBookRequestValidationError) ErrorName() string {
	return "AdminSearchBookRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AdminSearchBookRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdminSearchBookRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdminSearchBookRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdminSearchBookRequestValidationError{}

// Validate checks the field values on BookListPageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BookListPageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BookListPageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BookListPageResponseMultiError, or nil if none found.
func (m *BookListPageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BookListPageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBooks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BookListPageResponseValidationError{
						field:  fmt.Sprintf("Books[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BookListPageResponseValidationError{
						field:  fmt.Sprintf("Books[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BookListPageResponseValidationError{
					field:  fmt.Sprintf("Books[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetPage() < 1 {
		err := BookListPageResponseValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := BookListPageResponseValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Total

	if len(errors) > 0 {
		return BookListPageResponseMultiError(errors)
	}

	return nil
}

// BookListPageResponseMultiError is an error wrapping multiple validation
// errors returned by BookListPageResponse.ValidateAll() if the designated
// constraints aren't met.
type BookListPageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BookListPageResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BookListPageResponseMultiError) AllErrors() []error { return m }

// BookListPageResponseValidationError is the validation error returned by
// BookListPageResponse.Validate if the designated constraints aren't met.
type BookListPageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BookListPageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BookListPageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BookListPageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BookListPageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BookListPageResponseValidationError) ErrorName() string {
	return "BookListPageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BookListPageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBookListPageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BookListPageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BookListPageResponseValidationError{}

// Validate checks the field values on BookList with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BookList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BookList with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BookListMultiError, or nil
// if none found.
func (m *BookList) ValidateAll() error {
	return m.validate(true)
}

func (m *BookList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBooks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BookListValidationError{
						field:  fmt.Sprintf("Books[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BookListValidationError{
						field:  fmt.Sprintf("Books[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BookListValidationError{
					field:  fmt.Sprintf("Books[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BookListMultiError(errors)
	}

	return nil
}

// BookListMultiError is an error wrapping multiple validation errors returned
// by BookList.ValidateAll() if the designated constraints aren't met.
type BookListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BookListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BookListMultiError) AllErrors() []error { return m }

// BookListValidationError is the validation error returned by
// BookList.Validate if the designated constraints aren't met.
type BookListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BookListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BookListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BookListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BookListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BookListValidationError) ErrorName() string { return "BookListValidationError" }

// Error satisfies the builtin error interface
func (e BookListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBookList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BookListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BookListValidationError{}

// Validate checks the field values on OperateInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OperateInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OperateInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OperateInfoMultiError, or
// nil if none found.
func (m *OperateInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OperateInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Operater

	// no validation rules for Identity

	// no validation rules for OperateType

	// no validation rules for OperateDesc

	// no validation rules for OperateBookId

	// no validation rules for CreatedAt

	if len(errors) > 0 {
		return OperateInfoMultiError(errors)
	}

	return nil
}

// OperateInfoMultiError is an error wrapping multiple validation errors
// returned by OperateInfo.ValidateAll() if the designated constraints aren't met.
type OperateInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OperateInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OperateInfoMultiError) AllErrors() []error { return m }

// OperateInfoValidationError is the validation error returned by
// OperateInfo.Validate if the designated constraints aren't met.
type OperateInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OperateInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OperateInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OperateInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OperateInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OperateInfoValidationError) ErrorName() string { return "OperateInfoValidationError" }

// Error satisfies the builtin error interface
func (e OperateInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOperateInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OperateInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OperateInfoValidationError{}

// Validate checks the field values on BookOptLogsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BookOptLogsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BookOptLogsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BookOptLogsResponseMultiError, or nil if none found.
func (m *BookOptLogsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BookOptLogsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLogs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BookOptLogsResponseValidationError{
						field:  fmt.Sprintf("Logs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BookOptLogsResponseValidationError{
						field:  fmt.Sprintf("Logs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BookOptLogsResponseValidationError{
					field:  fmt.Sprintf("Logs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetPage() < 1 {
		err := BookOptLogsResponseValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := BookOptLogsResponseValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Total

	if len(errors) > 0 {
		return BookOptLogsResponseMultiError(errors)
	}

	return nil
}

// BookOptLogsResponseMultiError is an error wrapping multiple validation
// errors returned by BookOptLogsResponse.ValidateAll() if the designated
// constraints aren't met.
type BookOptLogsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BookOptLogsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BookOptLogsResponseMultiError) AllErrors() []error { return m }

// BookOptLogsResponseValidationError is the validation error returned by
// BookOptLogsResponse.Validate if the designated constraints aren't met.
type BookOptLogsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BookOptLogsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BookOptLogsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BookOptLogsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BookOptLogsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BookOptLogsResponseValidationError) ErrorName() string {
	return "BookOptLogsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BookOptLogsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBookOptLogsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BookOptLogsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BookOptLogsResponseValidationError{}

// Validate checks the field values on BookDownloadRecord with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BookDownloadRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BookDownloadRecord with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BookDownloadRecordMultiError, or nil if none found.
func (m *BookDownloadRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *BookDownloadRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetBookId() < 1 {
		err := BookDownloadRecordValidationError{
			field:  "BookId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetUserId() < 1 {
		err := BookDownloadRecordValidationError{
			field:  "UserId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CreatedAt

	// no validation rules for Title

	if len(errors) > 0 {
		return BookDownloadRecordMultiError(errors)
	}

	return nil
}

// BookDownloadRecordMultiError is an error wrapping multiple validation errors
// returned by BookDownloadRecord.ValidateAll() if the designated constraints
// aren't met.
type BookDownloadRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BookDownloadRecordMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BookDownloadRecordMultiError) AllErrors() []error { return m }

// BookDownloadRecordValidationError is the validation error returned by
// BookDownloadRecord.Validate if the designated constraints aren't met.
type BookDownloadRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BookDownloadRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BookDownloadRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BookDownloadRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BookDownloadRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BookDownloadRecordValidationError) ErrorName() string {
	return "BookDownloadRecordValidationError"
}

// Error satisfies the builtin error interface
func (e BookDownloadRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBookDownloadRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BookDownloadRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BookDownloadRecordValidationError{}

// Validate checks the field values on BookDownloadRecordResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BookDownloadRecordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BookDownloadRecordResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BookDownloadRecordResponseMultiError, or nil if none found.
func (m *BookDownloadRecordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BookDownloadRecordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BookDownloadRecordResponseValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BookDownloadRecordResponseValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BookDownloadRecordResponseValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetPage() < 1 {
		err := BookDownloadRecordResponseValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := BookDownloadRecordResponseValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Total

	if len(errors) > 0 {
		return BookDownloadRecordResponseMultiError(errors)
	}

	return nil
}

// BookDownloadRecordResponseMultiError is an error wrapping multiple
// validation errors returned by BookDownloadRecordResponse.ValidateAll() if
// the designated constraints aren't met.
type BookDownloadRecordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BookDownloadRecordResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BookDownloadRecordResponseMultiError) AllErrors() []error { return m }

// BookDownloadRecordResponseValidationError is the validation error returned
// by BookDownloadRecordResponse.Validate if the designated constraints aren't met.
type BookDownloadRecordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BookDownloadRecordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BookDownloadRecordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BookDownloadRecordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BookDownloadRecordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BookDownloadRecordResponseValidationError) ErrorName() string {
	return "BookDownloadRecordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BookDownloadRecordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBookDownloadRecordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BookDownloadRecordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BookDownloadRecordResponseValidationError{}

// Validate checks the field values on BookModifyRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BookModifyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BookModifyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BookModifyRequestMultiError, or nil if none found.
func (m *BookModifyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BookModifyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetBookId() < 1 {
		err := BookModifyRequestValidationError{
			field:  "BookId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Status

	// no validation rules for RecommendAt

	if all {
		switch v := interface{}(m.GetOperateInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BookModifyRequestValidationError{
					field:  "OperateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BookModifyRequestValidationError{
					field:  "OperateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOperateInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BookModifyRequestValidationError{
				field:  "OperateInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BookModifyRequestMultiError(errors)
	}

	return nil
}

// BookModifyRequestMultiError is an error wrapping multiple validation errors
// returned by BookModifyRequest.ValidateAll() if the designated constraints
// aren't met.
type BookModifyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BookModifyRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BookModifyRequestMultiError) AllErrors() []error { return m }

// BookModifyRequestValidationError is the validation error returned by
// BookModifyRequest.Validate if the designated constraints aren't met.
type BookModifyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BookModifyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BookModifyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BookModifyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BookModifyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BookModifyRequestValidationError) ErrorName() string {
	return "BookModifyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BookModifyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBookModifyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BookModifyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BookModifyRequestValidationError{}

// Validate checks the field values on BookId with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BookId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BookId with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in BookIdMultiError, or nil if none found.
func (m *BookId) ValidateAll() error {
	return m.validate(true)
}

func (m *BookId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetBookId() < 1 {
		err := BookIdValidationError{
			field:  "BookId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BookIdMultiError(errors)
	}

	return nil
}

// BookIdMultiError is an error wrapping multiple validation errors returned by
// BookId.ValidateAll() if the designated constraints aren't met.
type BookIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BookIdMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BookIdMultiError) AllErrors() []error { return m }

// BookIdValidationError is the validation error returned by BookId.Validate if
// the designated constraints aren't met.
type BookIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BookIdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BookIdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BookIdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BookIdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BookIdValidationError) ErrorName() string { return "BookIdValidationError" }

// Error satisfies the builtin error interface
func (e BookIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBookId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BookIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BookIdValidationError{}

// Validate checks the field values on BookIdPage with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BookIdPage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BookIdPage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BookIdPageMultiError, or
// nil if none found.
func (m *BookIdPage) ValidateAll() error {
	return m.validate(true)
}

func (m *BookIdPage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetBookId() < 1 {
		err := BookIdPageValidationError{
			field:  "BookId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPage() < 1 {
		err := BookIdPageValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := BookIdPageValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BookIdPageMultiError(errors)
	}

	return nil
}

// BookIdPageMultiError is an error wrapping multiple validation errors
// returned by BookIdPage.ValidateAll() if the designated constraints aren't met.
type BookIdPageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BookIdPageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BookIdPageMultiError) AllErrors() []error { return m }

// BookIdPageValidationError is the validation error returned by
// BookIdPage.Validate if the designated constraints aren't met.
type BookIdPageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BookIdPageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BookIdPageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BookIdPageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BookIdPageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BookIdPageValidationError) ErrorName() string { return "BookIdPageValidationError" }

// Error satisfies the builtin error interface
func (e BookIdPageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBookIdPage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BookIdPageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BookIdPageValidationError{}

// Validate checks the field values on ThemeBooksPageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ThemeBooksPageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThemeBooksPageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThemeBooksPageRequestMultiError, or nil if none found.
func (m *ThemeBooksPageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ThemeBooksPageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetThemeId() < 1 {
		err := ThemeBooksPageRequestValidationError{
			field:  "ThemeId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPage() < 1 {
		err := ThemeBooksPageRequestValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := ThemeBooksPageRequestValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ThemeBooksPageRequestMultiError(errors)
	}

	return nil
}

// ThemeBooksPageRequestMultiError is an error wrapping multiple validation
// errors returned by ThemeBooksPageRequest.ValidateAll() if the designated
// constraints aren't met.
type ThemeBooksPageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThemeBooksPageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThemeBooksPageRequestMultiError) AllErrors() []error { return m }

// ThemeBooksPageRequestValidationError is the validation error returned by
// ThemeBooksPageRequest.Validate if the designated constraints aren't met.
type ThemeBooksPageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThemeBooksPageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThemeBooksPageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThemeBooksPageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThemeBooksPageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThemeBooksPageRequestValidationError) ErrorName() string {
	return "ThemeBooksPageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ThemeBooksPageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThemeBooksPageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThemeBooksPageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThemeBooksPageRequestValidationError{}

// Validate checks the field values on BookInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BookInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BookInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BookInfoMultiError, or nil
// if none found.
func (m *BookInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BookInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BookId

	// no validation rules for Country

	// no validation rules for Lang

	// no validation rules for Title

	// no validation rules for Description

	// no validation rules for Cover

	// no validation rules for Type

	// no validation rules for AgeRange

	// no validation rules for RecommendedAt

	// no validation rules for Downloads

	// no validation rules for FromUserId

	// no validation rules for Status

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return BookInfoMultiError(errors)
	}

	return nil
}

// BookInfoMultiError is an error wrapping multiple validation errors returned
// by BookInfo.ValidateAll() if the designated constraints aren't met.
type BookInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BookInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BookInfoMultiError) AllErrors() []error { return m }

// BookInfoValidationError is the validation error returned by
// BookInfo.Validate if the designated constraints aren't met.
type BookInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BookInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BookInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BookInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BookInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BookInfoValidationError) ErrorName() string { return "BookInfoValidationError" }

// Error satisfies the builtin error interface
func (e BookInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBookInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BookInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BookInfoValidationError{}

// Validate checks the field values on ThemeBookTopkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ThemeBookTopkResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThemeBookTopkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThemeBookTopkResponseMultiError, or nil if none found.
func (m *ThemeBookTopkResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ThemeBookTopkResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBooks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ThemeBookTopkResponseValidationError{
						field:  fmt.Sprintf("Books[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ThemeBookTopkResponseValidationError{
						field:  fmt.Sprintf("Books[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ThemeBookTopkResponseValidationError{
					field:  fmt.Sprintf("Books[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ThemeBookTopkResponseMultiError(errors)
	}

	return nil
}

// ThemeBookTopkResponseMultiError is an error wrapping multiple validation
// errors returned by ThemeBookTopkResponse.ValidateAll() if the designated
// constraints aren't met.
type ThemeBookTopkResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThemeBookTopkResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThemeBookTopkResponseMultiError) AllErrors() []error { return m }

// ThemeBookTopkResponseValidationError is the validation error returned by
// ThemeBookTopkResponse.Validate if the designated constraints aren't met.
type ThemeBookTopkResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThemeBookTopkResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThemeBookTopkResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThemeBookTopkResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThemeBookTopkResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThemeBookTopkResponseValidationError) ErrorName() string {
	return "ThemeBookTopkResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ThemeBookTopkResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThemeBookTopkResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThemeBookTopkResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThemeBookTopkResponseValidationError{}

// Validate checks the field values on ThemeListResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ThemeListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThemeListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThemeListResponseMultiError, or nil if none found.
func (m *ThemeListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ThemeListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetThemes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ThemeListResponseValidationError{
						field:  fmt.Sprintf("Themes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ThemeListResponseValidationError{
						field:  fmt.Sprintf("Themes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ThemeListResponseValidationError{
					field:  fmt.Sprintf("Themes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ThemeListResponseMultiError(errors)
	}

	return nil
}

// ThemeListResponseMultiError is an error wrapping multiple validation errors
// returned by ThemeListResponse.ValidateAll() if the designated constraints
// aren't met.
type ThemeListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThemeListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThemeListResponseMultiError) AllErrors() []error { return m }

// ThemeListResponseValidationError is the validation error returned by
// ThemeListResponse.Validate if the designated constraints aren't met.
type ThemeListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThemeListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThemeListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThemeListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThemeListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThemeListResponseValidationError) ErrorName() string {
	return "ThemeListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ThemeListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThemeListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThemeListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThemeListResponseValidationError{}

// Validate checks the field values on Theme with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Theme) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Theme with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ThemeMultiError, or nil if none found.
func (m *Theme) ValidateAll() error {
	return m.validate(true)
}

func (m *Theme) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ThemeId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Cover

	// no validation rules for RecommendScore

	// no validation rules for BookCount

	if len(errors) > 0 {
		return ThemeMultiError(errors)
	}

	return nil
}

// ThemeMultiError is an error wrapping multiple validation errors returned by
// Theme.ValidateAll() if the designated constraints aren't met.
type ThemeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThemeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThemeMultiError) AllErrors() []error { return m }

// ThemeValidationError is the validation error returned by Theme.Validate if
// the designated constraints aren't met.
type ThemeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThemeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThemeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThemeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThemeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThemeValidationError) ErrorName() string { return "ThemeValidationError" }

// Error satisfies the builtin error interface
func (e ThemeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTheme.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThemeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThemeValidationError{}

// Validate checks the field values on ThemeIdRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ThemeIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThemeIdRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ThemeIdRequestMultiError,
// or nil if none found.
func (m *ThemeIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ThemeIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetThemeId() < 1 {
		err := ThemeIdRequestValidationError{
			field:  "ThemeId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ThemeIdRequestMultiError(errors)
	}

	return nil
}

// ThemeIdRequestMultiError is an error wrapping multiple validation errors
// returned by ThemeIdRequest.ValidateAll() if the designated constraints
// aren't met.
type ThemeIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThemeIdRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThemeIdRequestMultiError) AllErrors() []error { return m }

// ThemeIdRequestValidationError is the validation error returned by
// ThemeIdRequest.Validate if the designated constraints aren't met.
type ThemeIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThemeIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThemeIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThemeIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThemeIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThemeIdRequestValidationError) ErrorName() string { return "ThemeIdRequestValidationError" }

// Error satisfies the builtin error interface
func (e ThemeIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThemeIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThemeIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThemeIdRequestValidationError{}

// Validate checks the field values on ThemeDetailListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ThemeDetailListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThemeDetailListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThemeDetailListResponseMultiError, or nil if none found.
func (m *ThemeDetailListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ThemeDetailListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetThemes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ThemeDetailListResponseValidationError{
						field:  fmt.Sprintf("Themes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ThemeDetailListResponseValidationError{
						field:  fmt.Sprintf("Themes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ThemeDetailListResponseValidationError{
					field:  fmt.Sprintf("Themes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetPage() < 1 {
		err := ThemeDetailListResponseValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := ThemeDetailListResponseValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ThemeDetailListResponseMultiError(errors)
	}

	return nil
}

// ThemeDetailListResponseMultiError is an error wrapping multiple validation
// errors returned by ThemeDetailListResponse.ValidateAll() if the designated
// constraints aren't met.
type ThemeDetailListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThemeDetailListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThemeDetailListResponseMultiError) AllErrors() []error { return m }

// ThemeDetailListResponseValidationError is the validation error returned by
// ThemeDetailListResponse.Validate if the designated constraints aren't met.
type ThemeDetailListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThemeDetailListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThemeDetailListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThemeDetailListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThemeDetailListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThemeDetailListResponseValidationError) ErrorName() string {
	return "ThemeDetailListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ThemeDetailListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThemeDetailListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThemeDetailListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThemeDetailListResponseValidationError{}

// Validate checks the field values on Empty with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Empty) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Empty with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EmptyMultiError, or nil if none found.
func (m *Empty) ValidateAll() error {
	return m.validate(true)
}

func (m *Empty) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EmptyMultiError(errors)
	}

	return nil
}

// EmptyMultiError is an error wrapping multiple validation errors returned by
// Empty.ValidateAll() if the designated constraints aren't met.
type EmptyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmptyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmptyMultiError) AllErrors() []error { return m }

// EmptyValidationError is the validation error returned by Empty.Validate if
// the designated constraints aren't met.
type EmptyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmptyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmptyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmptyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmptyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmptyValidationError) ErrorName() string { return "EmptyValidationError" }

// Error satisfies the builtin error interface
func (e EmptyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmpty.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmptyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmptyValidationError{}

// Validate checks the field values on ShareBookRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ShareBookRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShareBookRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShareBookRequestMultiError, or nil if none found.
func (m *ShareBookRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ShareBookRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetBookId() < 1 {
		err := ShareBookRequestValidationError{
			field:  "BookId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ShareBookRequestMultiError(errors)
	}

	return nil
}

// ShareBookRequestMultiError is an error wrapping multiple validation errors
// returned by ShareBookRequest.ValidateAll() if the designated constraints
// aren't met.
type ShareBookRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShareBookRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShareBookRequestMultiError) AllErrors() []error { return m }

// ShareBookRequestValidationError is the validation error returned by
// ShareBookRequest.Validate if the designated constraints aren't met.
type ShareBookRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShareBookRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShareBookRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShareBookRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShareBookRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShareBookRequestValidationError) ErrorName() string { return "ShareBookRequestValidationError" }

// Error satisfies the builtin error interface
func (e ShareBookRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShareBookRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShareBookRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShareBookRequestValidationError{}
