# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: protos/ota.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'protos/ota.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10protos/ota.proto\x12\x15\x63om.aibook.admin.grpc\x1a\x1cgoogle/api/annotations.proto\"\x89\x01\n\x12\x43heckUpdateRequest\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x14\n\x0cversion_name\x18\x02 \x01(\t\x12\x14\n\x0cversion_code\x18\x03 \x01(\x05\x12\x11\n\tdevice_id\x18\x04 \x01(\t\x12\x12\n\nos_version\x18\x05 \x01(\t\x12\x10\n\x08language\x18\x06 \x01(\t\"\xbb\x01\n\x13\x43heckUpdateResponse\x12\x12\n\nhas_update\x18\x01 \x01(\x08\x12\x17\n\x0fis_force_update\x18\x02 \x01(\x08\x12\x14\n\x0cversion_name\x18\x03 \x01(\t\x12\x14\n\x0cversion_code\x18\x04 \x01(\x05\x12\x14\n\x0c\x64ownload_url\x18\x05 \x01(\t\x12\x14\n\x0cpackage_size\x18\x06 \x01(\x03\x12\x0b\n\x03md5\x18\x07 \x01(\t\x12\x12\n\nupdate_log\x18\x08 \x01(\t\"y\n\x1aReportUpgradeResultRequest\x12\x11\n\tisSucceed\x18\x01 \x01(\x08\x12\x1a\n\x12\x63urrentVersionName\x18\x02 \x01(\t\x12\x1a\n\x12\x63urrentVersionCode\x18\x03 \x01(\x05\x12\x10\n\x08\x64\x65viceId\x18\x04 \x01(\t\".\n\x1bReportUpgradeResultResponse\x12\x0f\n\x07message\x18\x01 \x01(\t2\xc6\x02\n\nOtaService\x12\x88\x01\n\x0b\x43heckUpdate\x12).com.aibook.admin.grpc.CheckUpdateRequest\x1a*.com.aibook.admin.grpc.CheckUpdateResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/api/v1/admin/check-update\x12\xac\x01\n\x13ReportUpgradeResult\x12\x31.com.aibook.admin.grpc.ReportUpgradeResultRequest\x1a\x32.com.aibook.admin.grpc.ReportUpgradeResultResponse\".\x82\xd3\xe4\x93\x02(\"#/api/v1/admin/report-upgrade-result:\x01*B\x11P\x01Z\r./admin;adminb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'protos.ota_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'P\001Z\r./admin;admin'
  _globals['_OTASERVICE'].methods_by_name['CheckUpdate']._loaded_options = None
  _globals['_OTASERVICE'].methods_by_name['CheckUpdate']._serialized_options = b'\202\323\344\223\002\034\022\032/api/v1/admin/check-update'
  _globals['_OTASERVICE'].methods_by_name['ReportUpgradeResult']._loaded_options = None
  _globals['_OTASERVICE'].methods_by_name['ReportUpgradeResult']._serialized_options = b'\202\323\344\223\002(\"#/api/v1/admin/report-upgrade-result:\001*'
  _globals['_CHECKUPDATEREQUEST']._serialized_start=74
  _globals['_CHECKUPDATEREQUEST']._serialized_end=211
  _globals['_CHECKUPDATERESPONSE']._serialized_start=214
  _globals['_CHECKUPDATERESPONSE']._serialized_end=401
  _globals['_REPORTUPGRADERESULTREQUEST']._serialized_start=403
  _globals['_REPORTUPGRADERESULTREQUEST']._serialized_end=524
  _globals['_REPORTUPGRADERESULTRESPONSE']._serialized_start=526
  _globals['_REPORTUPGRADERESULTRESPONSE']._serialized_end=572
  _globals['_OTASERVICE']._serialized_start=575
  _globals['_OTASERVICE']._serialized_end=901
# @@protoc_insertion_point(module_scope)
