package store

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"payment-backend/internal/domain/store"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"
	"payment-sdk/payment"
)

// packageService 流量包服务实现
type packageService struct {
	packageRepo store.PackageRepository
	gateways    map[string]payment.PaymentGateway
	logger      logger.Logger
}

// NewPackageService 创建流量包服务
func NewPackageService(packageRepo store.PackageRepository,
	gateways map[string]payment.PaymentGateway,
	logger logger.Logger) store.PackageService {
	return &packageService{
		packageRepo: packageRepo,
		gateways:    gateways,
		logger:      logger,
	}
}

// ListAllPackages 获取所有流量包（终端用户）
func (s *packageService) ListAllPackages(userCtx *middleware.UserContext, filter *store.PackageFilter, pagination *store.PaginationRequest) (*store.ListPackagesResponse, error) {
	// 如果没有传入过滤条件，使用默认值
	if filter == nil {
		filter = &store.PackageFilter{}
	}

	filter.SaleStatus = "on_sale"

	packages, total, err := s.packageRepo.List(filter, pagination)
	if err != nil {
		return nil, fmt.Errorf("failed to list packages: %w", err)
	}
	s.logger.Debug("ListAllPackages", logger.Int("len(packages)", len(packages)))

	// 转换为用户响应格式
	userPackages := make([]*store.PackageResponse, 0, len(packages))
	for _, pkg := range packages {
		userPackages = append(userPackages, pkg.ToUserResponse())
	}

	// 计算分页信息
	remaining := total - int64(pagination.Offset+len(userPackages))
	if remaining < 0 {
		remaining = 0
	}

	return &store.ListPackagesResponse{
		Packages: userPackages,
		Pagination: &store.PaginationResponse{
			Total:     total,
			Limit:     pagination.Limit,
			Offset:    pagination.Offset,
			Remaining: remaining,
		},
	}, nil
}

// 调用 s.packageRepo.GetByPackageID(req.PackageID) 获取一个流量包详情
func (s *packageService) GetPackageDetail(packageID string) (*store.PackageResponse, error) {
	pkg, err := s.packageRepo.GetByPackageID(packageID)
	if err != nil {
		return nil, fmt.Errorf("package not found: %w", err)
	}

	return pkg.ToUserResponse(), nil
}

// AdminAddPackages 添加流量包（管理员）
func (s *packageService) AdminAddPackages(req *store.CreatePackageRequest) (*store.AdminPackageResponse, error) {
	s.logger.Info("AdminAddPackages", logger.Object("req", req))

	// 创建新的流量包
	pkg := &store.Package{
		PackageID:         uuid.New().String(),
		PackageName:       req.PackageName,
		PackageDesc:       req.PackageDesc,
		PackageType:       req.PackageType,
		Entitlement:       req.Entitlement,
		EntitlementDesc:   req.EntitlementDesc,
		OriginalPrice:     req.OriginalPrice,
		DiscountPrice:     req.DiscountPrice,
		DiscountStartTime: req.DiscountStartTime,
		DiscountEndTime:   req.DiscountEndTime,
		DiscountDesc:      req.DiscountDesc,
		SaleStatus:        req.SaleStatus,
		Currency:          req.Currency,
		Country:           req.Country,
		PSPProductID:      req.PSPProductID,
		PSPProductDesc:    req.PSPProductDesc,
		PSPPriceID:        req.PSPPriceID,
		PSPPriceDesc:      req.PSPPriceDesc,
		ProductImg:        req.ProductImg,
		Extra1:            req.Extra1,
		Extra2:            req.Extra2,
		Extra3:            req.Extra3,
		Extra4:            req.Extra4,
		Deleted:           false,
	}

	// 设置默认值
	if pkg.SaleStatus == "" {
		pkg.SaleStatus = store.SaleStatusOnSale
	}

	// 验证价格逻辑
	if pkg.DiscountPrice != nil && *pkg.DiscountPrice >= pkg.OriginalPrice {
		return nil, fmt.Errorf("discount price must be less than original price")
	}

	// 验证优惠时间逻辑
	if pkg.DiscountStartTime != nil && pkg.DiscountEndTime != nil {
		if pkg.DiscountEndTime.Before(*pkg.DiscountStartTime) {
			return nil, fmt.Errorf("discount end time must be after start time")
		}
	}

	// 验证出售状态
	if pkg.SaleStatus != store.SaleStatusOnSale && pkg.SaleStatus != store.SaleStatusOffSale {
		return nil, fmt.Errorf("sale status must be 'on_sale' or 'off_sale'")
	}

	s.logger.Info("AdminAddPackages", logger.Object("s.gateways", s.gateways))
	var unitAmount int64
	if req.DiscountPrice != nil {
		unitAmount = int64(*req.DiscountPrice * 100)
		s.logger.Info("AdminAddPackages",
			zap.Float64("*req.DiscountPrice", *req.DiscountPrice),
			zap.Int64("unitAmount", unitAmount))
	} else {
		unitAmount = int64(req.OriginalPrice * 100)
		s.logger.Info("AdminAddPackages",
			zap.Float64("req.OriginalPrice", req.OriginalPrice),
			zap.Int64("unitAmount", unitAmount))
	}

	for _, gateway := range s.gateways {
		r := &payment.CreateOnetimeProductAndPriceReq{
			ProductName:        req.PackageName,
			ProductDescription: req.PackageDesc,
			UnitAmount:         unitAmount,
			Currency:           req.Currency,
		}
		s.logger.Debug("AdminAddPackages", logger.Object("CreateOnetimeProductAndPriceReq parms", r))
		rsp, err := gateway.CreateOnetimeProductAndPrice(r)
		if err != nil {
			s.logger.Warn("AdminAddPackages",
				logger.String("gateway.CreateOnetimeProductAndPrice", err.Error()))
			return nil, fmt.Errorf("failed to create product and price: %w", err)
		}
		s.logger.Debug("AdminAddPackages",
			logger.Object("gateway.CreateOnetimeProductAndPrice rsp", rsp))
		pkg.PSPProductID = rsp.ProductID
		pkg.PSPPriceID = rsp.PriceID
	}

	// 保存到数据库
	if err := s.packageRepo.Create(pkg); err != nil {
		return nil, fmt.Errorf("failed to create package: %w", err)
	}

	rsp := &store.AdminPackageResponse{
		PackageID:         pkg.PackageID,
		PackageName:       pkg.PackageName,
		PackageDesc:       pkg.PackageDesc,
		PackageType:       pkg.PackageType,
		Entitlement:       pkg.Entitlement,
		EntitlementDesc:   pkg.EntitlementDesc,
		OriginalPrice:     pkg.OriginalPrice,
		DiscountPrice:     pkg.DiscountPrice,
		DiscountStartTime: pkg.DiscountStartTime,
		DiscountEndTime:   pkg.DiscountEndTime,
		DiscountDesc:      pkg.DiscountDesc,
		SaleStatus:        pkg.SaleStatus,
		Currency:          pkg.Currency,
		Country:           pkg.Country,
		PSPProductID:      pkg.PSPProductID,
		PSPProductDesc:    pkg.PSPProductDesc,
		PSPPriceID:        pkg.PSPPriceID,
		PSPPriceDesc:      pkg.PSPPriceDesc,
		ProductImg:        pkg.ProductImg,
		Extra1:            pkg.Extra1,
		Extra2:            pkg.Extra2,
		Extra3:            pkg.Extra3,
		Extra4:            pkg.Extra4,
		CreatedAt:         pkg.CreatedAt,
		UpdatedAt:         pkg.UpdatedAt,
	}

	s.logger.Info("Package created successfully", logger.String("package_id", pkg.PackageID))
	return rsp, nil
}

// AdminDeletePackages 删除流量包（管理员）
func (s *packageService) AdminDeletePackages(req *store.DeletePackageRequest) error {

	// 这里理论上应该所有操作在一个事务中调用, 但是时间关系，暂时不处理.

	// 获取现有流量包
	pkg, err := s.packageRepo.GetByPackageID(req.PackageID)
	if err != nil {
		return fmt.Errorf("package not found: %w", err)
	}
	s.logger.Debug("AdminDeletePackages",
		logger.Object("s.packageRepo.GetByPackageID pkg", pkg))

	// 软删除
	if err := s.packageRepo.SoftDelete(req.PackageID); err != nil {
		s.logger.Warn("AdminDeletePackages",
			logger.String("s.packageRepo.SoftDelete error", err.Error()))
		return fmt.Errorf("failed to delete package: %w", err)
	}

	for _, gateway := range s.gateways {
		err := gateway.DeleteProductAndDisablePrice(pkg.PSPProductID, pkg.PSPPriceID)
		if err != nil {
			s.logger.Warn("AdminDeletePackages",
				logger.String("gateway.DeleteProductAndDisablePrice error", err.Error()))
			return err
		}
		s.logger.Debug("AdminDeletePackages, gateway.DeleteProductAndDisablePrice success",
			logger.String("package_id", req.PackageID))
	}

	s.logger.Info("Package deleted successfully", logger.String("package_id", req.PackageID))
	return nil
}

// AdminUpdatePackages 更新流量包（管理员）
func (s *packageService) AdminUpdatePackages(req *store.UpdatePackageRequest) error {
	// 获取现有流量包
	pkg, err := s.packageRepo.GetByPackageID(req.PackageID)
	if err != nil {
		s.logger.Warn("AdminUpdatePackages",
			logger.String("s.packageRepo.GetByPackageID error", err.Error()))
		return fmt.Errorf("package not found: %w", err)
	}

	// 更新字段
	if req.PackageName != nil {
		pkg.PackageName = *req.PackageName
	}
	if req.PackageDesc != nil {
		pkg.PackageDesc = *req.PackageDesc
	}
	if req.PackageType != nil {
		pkg.PackageType = *req.PackageType
	}
	if req.Entitlement != nil {
		pkg.Entitlement = *req.Entitlement
	}
	if req.EntitlementDesc != nil {
		pkg.EntitlementDesc = *req.EntitlementDesc
	}
	if req.OriginalPrice != nil {
		pkg.OriginalPrice = *req.OriginalPrice
	}
	if req.DiscountPrice != nil {
		pkg.DiscountPrice = req.DiscountPrice
	}
	if req.DiscountStartTime != nil {
		pkg.DiscountStartTime = req.DiscountStartTime
	}
	if req.DiscountEndTime != nil {
		pkg.DiscountEndTime = req.DiscountEndTime
	}
	if req.DiscountDesc != nil {
		pkg.DiscountDesc = *req.DiscountDesc
	}
	if req.SaleStatus != nil {
		pkg.SaleStatus = *req.SaleStatus
	}
	if req.Currency != nil {
		pkg.Currency = *req.Currency
	}
	if req.Country != nil {
		pkg.Country = *req.Country
	}
	if req.PSPProductID != nil {
		pkg.PSPProductID = *req.PSPProductID
	}
	if req.PSPProductDesc != nil {
		pkg.PSPProductDesc = *req.PSPProductDesc
	}
	if req.PSPPriceID != nil {
		pkg.PSPPriceID = *req.PSPPriceID
	}
	if req.PSPPriceDesc != nil {
		pkg.PSPPriceDesc = *req.PSPPriceDesc
	}
	if req.ProductImg != nil {
		pkg.ProductImg = *req.ProductImg
	}
	if req.Extra1 != nil {
		pkg.Extra1 = *req.Extra1
	}
	if req.Extra2 != nil {
		pkg.Extra2 = *req.Extra2
	}
	if req.Extra3 != nil {
		pkg.Extra3 = *req.Extra3
	}
	if req.Extra4 != nil {
		pkg.Extra4 = *req.Extra4
	}

	// 验证价格逻辑
	if pkg.DiscountPrice != nil && *pkg.DiscountPrice >= pkg.OriginalPrice {
		return fmt.Errorf("discount price must be less than original price")
	}

	// 验证优惠时间逻辑
	if pkg.DiscountStartTime != nil && pkg.DiscountEndTime != nil {
		if pkg.DiscountEndTime.Before(*pkg.DiscountStartTime) {
			return fmt.Errorf("discount end time must be after start time")
		}
	}

	// 验证出售状态
	if pkg.SaleStatus != store.SaleStatusOnSale && pkg.SaleStatus != store.SaleStatusOffSale {
		return fmt.Errorf("sale status must be 'on_sale' or 'off_sale'")
	}

	// 更新时间
	now := time.Now()
	pkg.UpdatedAt = &now

	var unitAmount int64
	if pkg.DiscountPrice != nil {
		unitAmount = int64(*pkg.DiscountPrice * 100)
		s.logger.Info("AdminAddPackages",
			zap.Float64("*pkg.DiscountPrice", *pkg.DiscountPrice),
			zap.Int64("unitAmount", unitAmount))
	} else {
		unitAmount = int64(pkg.OriginalPrice * 100)
		s.logger.Info("AdminAddPackages",
			zap.Float64("pkg.OriginalPrice", pkg.OriginalPrice),
			zap.Int64("unitAmount", unitAmount))
	}

	for _, gateway := range s.gateways {
		r := &payment.UpdateOnetimeProductAndPriceReq{
			ProductID:          pkg.PSPProductID,
			PriceID:            pkg.PSPPriceID,
			ProductName:        pkg.PackageName,
			ProductDescription: pkg.PackageDesc,
			UnitAmount:         unitAmount,
			Currency:           pkg.Currency,
		}
		rsp, err := gateway.UpdateProductAndPrice(r)
		if err != nil {
			s.logger.Warn("AdminUpdatePackages",
				logger.String("gateway.UpdateProductAndPrice error", err.Error()))
			return err
		}
		s.logger.Debug("AdminUpdatePackages",
			logger.Object("gateway.UpdateProductAndPrice rsp", rsp))
		if rsp.PriceIDRefeshed {
			pkg.PSPPriceID = rsp.PriceID
			s.logger.Info("AdminUpdatePackages",
				logger.String("gateway.UpdateProductAndPrice price refreshed", "true"),
				logger.String("new_price_id", rsp.PriceID))
		}
	}

	// 保存到数据库
	if err := s.packageRepo.Update(pkg); err != nil {
		s.logger.Warn("AdminUpdatePackages",
			logger.String("s.packageRepo.Update error", err.Error()))
		return fmt.Errorf("failed to update package: %w", err)
	}

	s.logger.Info("Package updated successfully", logger.String("package_id", req.PackageID))
	return nil
}

// AdminListAllPackages 获取所有流量包（管理员）
func (s *packageService) AdminListAllPackages(filter *store.PackageFilter, pagination *store.PaginationRequest) (*store.AdminListPackagesResponse, error) {
	// 如果没有传入过滤条件，使用默认值
	if filter == nil {
		filter = &store.PackageFilter{}
	}

	packages, total, err := s.packageRepo.List(filter, pagination)
	if err != nil {
		s.logger.Warn("AdminListAllPackages",
			logger.String("s.packageRepo.List error", err.Error()))
		return nil, fmt.Errorf("failed to list packages: %w", err)
	}
	s.logger.Debug("AdminListAllPackages", logger.Int("len(packages)", len(packages)))

	// 转换为管理员响应格式
	adminPackages := make([]*store.AdminPackageResponse, 0, len(packages))
	for _, pkg := range packages {
		adminPackages = append(adminPackages, pkg.ToAdminResponse())
	}

	// 计算分页信息
	remaining := total - int64(pagination.Offset+len(adminPackages))
	if remaining < 0 {
		remaining = 0
	}

	return &store.AdminListPackagesResponse{
		Packages: adminPackages,
		Pagination: &store.PaginationResponse{
			Total:     total,
			Limit:     pagination.Limit,
			Offset:    pagination.Offset,
			Remaining: remaining,
		},
	}, nil
}
