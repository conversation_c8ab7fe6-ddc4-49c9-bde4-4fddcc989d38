package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/rand/v2"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	dubbo_constant "dubbo.apache.org/dubbo-go/v3/common/constant"

	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"go.uber.org/zap"

	"payment-common/logger"
	"payment-common/config"

	"payment-backend/internal/dubbo/shared-protos/gen/go/userpb"
)

const (
	UserService_ServiceName = "user-service"
)

// PointServiceHTTP HTTP方式积分服务接口
type PointServiceHTTP interface {
	WithUserContext(ctx context.Context, userId string, country, traceId string) context.Context
	// ChargePoints 通过HTTP调用用户服务充值积分
	ChargePoints(ctx context.Context, points float32, reason string) error
}

// pointServiceHTTP HTTP方式积分服务实现
type pointServiceHTTP struct {
	config     *config.Config
	logger     logger.Logger
	httpClient *http.Client
}

// ChargePointsHTTPRequest HTTP充值积分请求
type ChargePointsHTTPRequest struct {
	Points float32 `json:"points"` // 积分数
	Reason string  `json:"reason"` // 充值原因
}

// ChargePointsHTTPResponse HTTP充值积分响应
type ChargePointsHTTPResponse struct {
	// 根据用户服务的实际响应结构调整
	Success bool   `json:"success,omitempty"`
	Message string `json:"message,omitempty"`
}

// NewPointServiceHTTP 创建HTTP方式积分服务
func NewPointServiceHTTP(config *config.Config, logger logger.Logger) PointServiceHTTP {
	return &pointServiceHTTP{
		config: config,
		logger: logger,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// initHTTPClient 初始化HTTP客户端（懒加载）
func (s *pointServiceHTTP) initHTTPClient() ([]string, error) {

	// 如果已经初始化过，直接返回
	// if s.baseURL != "" {
	// 	return nil
	// }

	// 检查 Nacos 是否启用
	if !s.config.Nacos.Enabled {
		return nil, fmt.Errorf("nacos is not enabled, cannot get user service HTTP address")
	}

	// 从 Nacos 配置中心获取用户服务的 HTTP 地址
	baseURLs, err := s.getBaseURLFromNacosConfig()
	if err != nil {
		return nil, err
	}

	s.logger.Info("HTTP client initialized successfully", zap.Any("baseURLs", baseURLs))
	return baseURLs, nil
}

// getBaseURLFromNacosConfig 从Nacos配置中心获取用户服务地址
func (s *pointServiceHTTP) getBaseURLFromNacosConfig() ([]string, error) {
	// 创建 nacos client config
	clientConfig := *constant.NewClientConfig(
		constant.WithNamespaceId(s.config.Nacos.Namespace), // 替换为你的 namespaceId
		constant.WithUsername(s.config.Nacos.Username),     // 替换为你的用户名
		constant.WithPassword(s.config.Nacos.Password),     // 替换为你的密码
		constant.WithTimeoutMs(6000),
		constant.WithNotLoadCacheAtStart(true),
		constant.WithLogLevel("info"),
	)

	serverConfigs := make([]constant.ServerConfig, 0)
	for _, ep := range s.config.Nacos.Endpoints {
		parts := strings.Split(ep, ":")
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid endpoint format: %s", ep)
		}
		port, err := strconv.Atoi(parts[1])
		if err != nil {
			return nil, fmt.Errorf("invalid port in endpoint %s: %v", ep, err)
		}
		serverConfigs = append(serverConfigs, constant.ServerConfig{
			IpAddr: parts[0],
			Port:   uint64(port),
		})
	}

	// 创建 naming client
	namingClient, err := clients.NewNamingClient(
		vo.NacosClientParam{
			ClientConfig:  &clientConfig,
			ServerConfigs: serverConfigs,
		},
	)
	if err != nil {
		s.logger.Warn("clients.NewNamingClient failed", zap.Error(err))
		return nil, err
	}

	// 获取 user-service 实例列表
	serviceName := UserService_ServiceName
	if env := os.Getenv("PAYMENT_NACOS_USERSERVICE_SERVICENAME"); env != "" { // 时间关系, 暂时写到这里.
		serviceName = env
	}

	instances, err := namingClient.SelectInstances(vo.SelectInstancesParam{
		ServiceName: serviceName,
		GroupName:   s.config.Nacos.Config.Group,
		HealthyOnly: true,
	})
	if err != nil {
		s.logger.Warn("namingClient.SelectInstances failed", zap.Error(err))
		return nil, err
	}

	if len(instances) == 0 {
		s.logger.Warn("len(instances)==0")
		return nil, fmt.Errorf("len(instances)==0")
	}

	res := make([]string, 0)
	for i, ins := range instances {
		s.logger.Debug("user-service instance",
			zap.Int("index", i),
			zap.String("ins.Ip", ins.Ip),
			zap.Uint64("ins.Port", ins.Port),
			zap.Any("ins.Metadata", ins.Metadata))

		res = append(res, fmt.Sprintf("%v:%v", ins.Ip, ins.Port))
	}
	return res, nil
}

func (s *pointServiceHTTP) WithUserContext(ctx context.Context, userIdStr string, country, traceId string) context.Context {
	v := map[string]any{"x-user-id": []string{userIdStr},
		"x-country":  []string{country},
		"x-trace-id": []string{traceId}}
	return context.WithValue(ctx, dubbo_constant.AttachmentKey, v)
}

// ChargePoints 通过HTTP调用用户服务充值积分
func (s *pointServiceHTTP) ChargePoints(ctx context.Context, points float32, reason string) error {
	// 验证参数
	if points > 100000000 {
		return fmt.Errorf("points must be less than or equal to 100000000")
	}
	if len(reason) == 0 || len(reason) > 1024 {
		return fmt.Errorf("reason length must be between 1 and 1024 characters")
	}

	// 初始化HTTP客户端
	baseURLs, err := s.initHTTPClient()
	if err != nil {
		return fmt.Errorf("failed to initialize HTTP client: %w", err)
	}
	rand.Shuffle(len(baseURLs), func(i, j int) {
		baseURLs[i], baseURLs[j] = baseURLs[j], baseURLs[i]
	})

	// 构建请求
	req := &ChargePointsHTTPRequest{
		Points: points,
		Reason: reason,
	}

	// 发送HTTP请求
	var errRet error
	for _, baseUrl := range baseURLs {
		s.logger.Info("Calling user service ChargePoints via HTTP",
			zap.Float32("points", points),
			zap.String("reason", reason),
			zap.String("baseUrl", baseUrl))

		if err := s.sendChargePointsRequest(ctx, baseUrl, req); err != nil {
			errRet = err
			if err != nil {
				s.logger.Warn("sendChargePointsRequest failed",
					zap.Any("ctx", ctx),
					zap.Any("req", req),
					zap.Error(err))
			} else {
				break
			}
		}
	}

	if errRet != nil {
		return errRet
	}
	s.logger.Info("Points charged successfully via HTTP",
		zap.Float32("points", points),
		zap.String("reason", reason))
	return nil
}

// sendChargePointsRequest 发送充值积分HTTP请求
func (s *pointServiceHTTP) sendChargePointsRequest(ctx context.Context, baseUrl string, req *ChargePointsHTTPRequest) error {
	chargePointsRequestPath := userpb.UserService_ChargePoints_FullMethodName
	url := baseUrl + chargePointsRequestPath
	if strings.Index(url, "http://") != 0 {
		url = "http://" + url
	}

	// 序列化请求体
	jsonData, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	userId := ""

	// 从上下文中获取用户信息并设置到请求头
	if attachments := ctx.Value(dubbo_constant.AttachmentKey); attachments != nil {
		if attachmentMap, ok := attachments.(map[string]any); ok {
			if userIds, exists := attachmentMap["x-user-id"]; exists {
				if userIdSlice, ok := userIds.([]string); ok && len(userIdSlice) > 0 {
					userId = userIdSlice[0]
					httpReq.Header.Set("x-user-id", userIdSlice[0])
				}
			}
			if countries, exists := attachmentMap["x-country"]; exists {
				if countrySlice, ok := countries.([]string); ok && len(countrySlice) > 0 {
					httpReq.Header.Set("x-country", countrySlice[0])
				}
			}
			if traceIds, exists := attachmentMap["x-trace-id"]; exists {
				if traceIdSlice, ok := traceIds.([]string); ok && len(traceIdSlice) > 0 {
					httpReq.Header.Set("x-trace-id", traceIdSlice[0])
				}
			}
		}
	}

	// 发送请求
	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to send HTTP request to %s(userId:%v): %w", baseUrl, userId, err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body(userId:%v): %w", userId, err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP request failed with status(userId:%v) %d: %s", userId, resp.StatusCode, string(body))
	}

	// // 解析响应（根据用户服务的实际响应格式调整）
	// var response ChargePointsHTTPResponse
	// if err := json.Unmarshal(body, &response); err != nil {
	// 	// 如果解析失败，可能是因为响应格式不同，记录日志但不返回错误
	// 	s.logger.Warn("Failed to unmarshal response, but HTTP status is OK",
	// 		zap.String("response_body", string(body)),
	// 		zap.Error(err))
	// }

	s.logger.Info("sendChargePointsRequest successfully",
		zap.String("userId", userId),
		zap.Int("status_code", resp.StatusCode),
		zap.String("response_body", string(body)))

	return nil
}
