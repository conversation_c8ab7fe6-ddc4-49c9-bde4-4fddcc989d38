{"swagger": "2.0", "info": {"title": "protos/market.proto", "version": "version not set"}, "tags": [{"name": "MarketService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/market/admin/search": {"get": {"summary": "管理平台搜索绘本 --- 管理平台调用", "operationId": "MarketService_AdminSearchBook", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcBookListPageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "description": "页码", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "size", "description": "每页数量", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "orderBy", "description": "排序字段( 0-国家、1-主题、2-状态、3-年龄段、4-名称筛选)", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "isDesc", "description": "是否降序", "in": "query", "required": false, "type": "boolean"}, {"name": "searchByTitle", "in": "query", "required": false, "type": "string"}, {"name": "searchStatus", "in": "query", "required": false, "type": "string"}, {"name": "searchByDescription", "in": "query", "required": false, "type": "string"}, {"name": "searchByAgeRange", "in": "query", "required": false, "type": "string"}, {"name": "searchByCountry", "in": "query", "required": false, "type": "string"}, {"name": "searchByRecommend", "description": "是否推荐(yes or no)", "in": "query", "required": false, "type": "string"}], "tags": ["MarketService"]}}, "/api/v1/market/book/download/record": {"get": {"summary": "查询绘本下载记录 --- 管理平台调用", "operationId": "MarketService_GetBookDownloadRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcBookDownloadRecordResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "userId", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "page", "description": "页码", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "size", "description": "每页数量", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["MarketService"]}}, "/api/v1/market/book/download/record/sync": {"post": {"summary": "同步下载记录 --- 绘本终端调用", "operationId": "MarketService_SyncBookDownloadRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "bookId", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["MarketService"]}}, "/api/v1/market/book/opt/logs": {"get": {"summary": "操作日志查询 --- 管理平台调用", "operationId": "MarketService_BookOptLogs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcBookOptLogsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "bookId", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "page", "description": "页码", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "size", "description": "每页数量", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["MarketService"]}}, "/api/v1/market/book/{bookId}": {"get": {"summary": "绘本详情", "operationId": "MarketService_BookDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcBookInfo"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "bookId", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["MarketService"]}, "post": {"summary": "绘本属性修改(如审核、发布、下架, 删除) -- 管理平台调用", "operationId": "MarketService_BookModify", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "bookId", "description": "绘本ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/MarketServiceBookModifyBody"}}], "tags": ["MarketService"]}}, "/api/v1/market/recommend/books": {"get": {"summary": "推荐绘本查询(根据国家和年龄段筛选) --- 绘本终端调用", "operationId": "MarketService_GetRecommendBooks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcBookList"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["MarketService"]}}, "/api/v1/market/search": {"get": {"summary": "搜索绘本（按主题、按标题）， 按热度排序返回 --- 绘本终端调用", "operationId": "MarketService_SearchBook", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcBookListPageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "searchStr", "in": "query", "required": false, "type": "string"}, {"name": "page", "description": "页码", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "size", "description": "每页数量", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["MarketService"]}}, "/api/v1/market/share": {"post": {"summary": "共享绘本", "operationId": "MarketService_ShareBook", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcShareBookRequest"}}], "tags": ["MarketService"]}}, "/api/v1/market/theme/book/topk": {"get": {"summary": "绘本主题下绘本 topK (按国家和年龄段筛选， 全量返回) --- 绘本终端调用", "operationId": "MarketService_ThemeBookTopk", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcBookList"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "themeId", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["MarketService"]}}, "/api/v1/market/theme/books": {"get": {"summary": "绘本主题下绘本列表(按国家和年龄段筛选， 分页返回) --- 绘本终端调用", "operationId": "MarketService_GetThemeBooks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcBookListPageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "themeId", "description": "绘本主题ID", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "page", "description": "页码", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "size", "description": "每页数量", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "total", "description": "总数", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["MarketService"]}}, "/api/v1/market/theme/list": {"get": {"summary": "绘本主题列表(全量返回) --- 管理平台调用", "operationId": "MarketService_GetThemeList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcThemeDetailListResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["MarketService"]}}, "/api/v1/market/theme/topk": {"get": {"summary": "绘本主题 topK (按国家和年龄段筛选， 全量返回) --- 绘本终端调用", "operationId": "MarketService_ThemeTopk", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcThemeListResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["MarketService"]}}}, "definitions": {"MarketServiceBookModifyBody": {"type": "object", "properties": {"themeIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "绘本主题, 空表示不修改"}, "status": {"type": "string", "title": "绘本状态, 空表示不修改"}, "recommendAt": {"type": "string", "title": "推荐时间, 空表示不修改， 0-表示不推荐，秒级时间戳-表示推荐"}, "operateInfo": {"$ref": "#/definitions/grpcOperateInfo", "title": "操作信息"}}}, "grpcBookDownloadRecord": {"type": "object", "properties": {"bookId": {"type": "string", "format": "uint64"}, "userId": {"type": "string", "format": "uint64"}, "createdAt": {"type": "integer", "format": "int64"}, "title": {"type": "string", "title": "绘本标题"}}}, "grpcBookDownloadRecordResponse": {"type": "object", "properties": {"records": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/grpcBookDownloadRecord"}}, "page": {"type": "integer", "format": "int64", "title": "页码"}, "size": {"type": "integer", "format": "int64", "title": "每页数量"}, "total": {"type": "integer", "format": "int64", "title": "总数"}}}, "grpcBookInfo": {"type": "object", "properties": {"bookId": {"type": "string", "format": "uint64", "title": "绘本ID"}, "country": {"type": "string", "title": "绘本国家"}, "lang": {"type": "string", "title": "绘本语言"}, "title": {"type": "string", "title": "绘本标题"}, "description": {"type": "string", "title": "绘本描述"}, "cover": {"type": "string", "title": "绘本封面"}, "type": {"type": "string", "title": "绘本类型：图文、动态"}, "ageRange": {"type": "string", "title": "绘本年龄段"}, "recommendedAt": {"type": "integer", "format": "int64", "title": "绘本推荐时间, 值为0表示不推荐"}, "downloads": {"type": "integer", "format": "int64", "title": "绘本下载次数"}, "fromUserId": {"type": "string", "format": "uint64", "title": "绘本作者ID"}, "status": {"type": "string", "title": "绘本状态"}, "themes": {"type": "array", "items": {"type": "string"}, "title": "绘本主题"}, "createdAt": {"type": "integer", "format": "int64", "title": "绘本进入绘本市场时间"}, "updatedAt": {"type": "integer", "format": "int64", "title": "最后更新时间"}}}, "grpcBookList": {"type": "object", "properties": {"books": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/grpcBookInfo"}}}}, "grpcBookListPageResponse": {"type": "object", "properties": {"books": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/grpcBookInfo"}}, "page": {"type": "integer", "format": "int64", "title": "页码"}, "size": {"type": "integer", "format": "int64", "title": "每页数量"}, "total": {"type": "integer", "format": "int64", "title": "总数"}}}, "grpcBookOptLogsResponse": {"type": "object", "properties": {"logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/grpcOperateInfo"}}, "page": {"type": "integer", "format": "int64", "title": "页码"}, "size": {"type": "integer", "format": "int64", "title": "每页数量"}, "total": {"type": "integer", "format": "int64", "title": "总数"}}}, "grpcEmpty": {"type": "object"}, "grpcOperateInfo": {"type": "object", "properties": {"operater": {"type": "string", "title": "操作者"}, "identity": {"type": "string", "title": "身份"}, "operateType": {"type": "string", "title": "操作类型(审核、发布、下架、删除等)"}, "operateDesc": {"type": "string", "title": "操作描述或结论(审批未通过的原因等)"}, "operateBookId": {"type": "string", "format": "uint64", "title": "操作的绘本"}, "createdAt": {"type": "integer", "format": "int64", "title": "操作时间"}}}, "grpcShareBookRequest": {"type": "object", "properties": {"bookId": {"type": "string", "format": "uint64", "title": "绘本ID"}}}, "grpcTheme": {"type": "object", "properties": {"themeId": {"type": "integer", "format": "int64", "title": "绘本主题ID"}, "name": {"type": "string", "title": "绘本主题名称"}, "description": {"type": "string", "title": "绘本主题描述"}, "cover": {"type": "string", "title": "绘本主题封面"}, "recommendScore": {"type": "integer", "format": "int64", "title": "绘本主题推荐分数"}, "bookCount": {"type": "integer", "format": "int64", "title": "绘本数量"}}}, "grpcThemeDetailListResponse": {"type": "object", "properties": {"themes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/grpcTheme"}, "title": "绘本主题数组"}, "page": {"type": "integer", "format": "int64", "title": "页码"}, "size": {"type": "integer", "format": "int64", "title": "每页数量"}, "total": {"type": "integer", "format": "int64", "title": "总数"}}}, "grpcThemeListResponse": {"type": "object", "properties": {"themes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/grpcTheme"}, "title": "绘本主题数组"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}