{"swagger": "2.0", "info": {"title": "protos/protocol.proto", "version": "version not set"}, "tags": [{"name": "ProtocolService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/admin/protocol": {"get": {"operationId": "ProtocolService_getProtocol", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcProtocolResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "country", "description": "国家/地区", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "协议类型\n1：用户隐私协议 2：支付协议 3：创作绘本协议", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["ProtocolService"]}}, "/api/v1/admin/protocol/confirm": {"post": {"summary": "用户协议确认", "operationId": "ProtocolService_confirmProtocol", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcConfirmProtocolResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcConfirmProtocolRequest"}}], "tags": ["ProtocolService"]}}}, "definitions": {"grpcConfirmProtocolRequest": {"type": "object", "properties": {"userId": {"type": "string", "title": "用户id"}, "userEmail": {"type": "string", "title": "用户邮箱"}, "protocolId": {"type": "string", "title": "协议id"}}}, "grpcConfirmProtocolResponse": {"type": "object"}, "grpcProtocolResponse": {"type": "object", "properties": {"protocolId": {"type": "integer", "format": "int32", "title": "协议id"}, "content": {"type": "string", "title": "协议内容，为富文本格式，带段落标识"}, "version": {"type": "integer", "format": "int32", "title": "协议版本，一般获取的都是最新版本"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}