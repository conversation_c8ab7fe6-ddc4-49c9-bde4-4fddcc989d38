package service

import (
	"context"
	"fmt"
	"sync"

	"dubbo.apache.org/dubbo-go/v3/client"
	"dubbo.apache.org/dubbo-go/v3/common/constant"
	"dubbo.apache.org/dubbo-go/v3/registry"
	"go.uber.org/zap"

	"payment-backend/internal/dubbo/shared-protos/gen/go/userpb"
	"payment-common/config"
	"payment-common/logger"
)

// PointService 积分服务接口
type PointService interface {
	WithUserContext(ctx context.Context, userId string, country, traceId string) context.Context
	// ChargePoints 调用用户服务充值积分
	ChargePoints(ctx context.Context, points float32, reason string) error
}

// pointService 积分服务实现
type pointService struct {
	config     *config.Config
	logger     logger.Logger
	userClient userpb.UserService
	clientMu   sync.Mutex
}

// NewPointService 创建积分服务
func NewPointService(config *config.Config, logger logger.Logger) PointService {
	return &pointService{
		config: config,
		logger: logger,
	}
}

// initUserClient 初始化用户服务客户端（懒加载）
func (s *pointService) initUserClient() error {
	s.clientMu.Lock()
	defer s.clientMu.Unlock()

	// 检查 Nacos 是否启用
	if !s.config.Nacos.Enabled {
		return fmt.Errorf("nacos is not enabled, cannot create user service client")
	}

	// 构建 Nacos 地址
	nacosAddress := fmt.Sprintf("nacos://%s", s.config.Nacos.Endpoints[0])

	// 创建 Dubbo 客户端
	cli, err := client.NewClient(
		client.WithClientRegistry(
			registry.WithNacos(),
			registry.WithAddress(nacosAddress),
			registry.WithNamespace(s.config.Nacos.Namespace),
			registry.WithUsername(s.config.Nacos.Username),
			registry.WithPassword(s.config.Nacos.Password),
		),
		client.WithClientRetries(4),
	)
	if err != nil {
		s.logger.Warn("initUserClient, failed to create dubbo client",
			zap.String("err", err.Error()),
		)
		return fmt.Errorf("failed to create dubbo client: %w", err)
	}
	if cli == nil {
		s.logger.Warn("cli==nil")
		return fmt.Errorf("cli==nil")
	}
	s.logger.Info("initUserClient success")

	// 创建用户服务客户端
	userClient, err := userpb.NewUserService(cli)
	if err != nil {
		return fmt.Errorf("failed to create user service client: %w", err)
	}
	if userClient == nil {
		s.logger.Warn("userClient==nil")
		return fmt.Errorf("userClient==nil")
	}

	s.userClient = userClient
	s.logger.Info("User service client initialized successfully")

	return nil
}

func (s *pointService) WithUserContext(ctx context.Context, userIdStr string, country, traceId string) context.Context {
	v := map[string]any{"x-user-id": []string{userIdStr},
		"x-country":  []string{country},
		"x-trace-id": []string{traceId}}
	return context.WithValue(ctx, constant.AttachmentKey, v)
}

// ChargePoints 调用用户服务充值积分
func (s *pointService) ChargePoints(ctx context.Context, points float32, reason string) error {
	// 验证参数
	if points > 100000000 {
		return fmt.Errorf("points must be less than or equal to 100000000")
	}
	if len(reason) == 0 || len(reason) > 1024 {
		return fmt.Errorf("reason length must be between 1 and 1024 characters")
	}

	// 初始化客户端
	if err := s.initUserClient(); err != nil {
		return fmt.Errorf("failed to initialize user client: %w", err)
	}

	// 构建请求
	req := &userpb.ChargeRequest{
		Points: points,
		Reason: reason,
	}

	s.logger.Info("Calling user service ChargePoints",
		zap.Float32("points", points),
		zap.String("reason", reason))

	// 调用用户服务
	if s.userClient == nil {
		s.logger.Warn("s.userClient == nil")
		return fmt.Errorf("user client is nil, possibly initUserClient failed silently")
	}
	_, err := s.userClient.ChargePoints(ctx, req)
	if err != nil {
		s.logger.Error("Failed to charge points via user service",
			zap.Float32("points", points),
			zap.String("reason", reason),
			zap.Error(err))
		return fmt.Errorf("failed to charge points: %w", err)
	}

	s.logger.Info("Points charged successfully",
		zap.Float32("points", points),
		zap.String("reason", reason))

	return nil
}
