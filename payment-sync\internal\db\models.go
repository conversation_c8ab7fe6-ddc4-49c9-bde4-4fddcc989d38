package db

import (
	"time"

	"gorm.io/gorm"
)

// SyncHistoryModel 同步历史记录模型
type SyncHistoryModel struct {
	ID         uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	OrderID    string     `gorm:"type:varchar(64);not null;index:idx_sync_history_order_id" json:"order_id"`
	SyncType   string     `gorm:"type:varchar(20);not null;index:idx_sync_history_sync_type" json:"sync_type"` // payment_fulfillment, refund_fulfillment
	Status     string     `gorm:"type:varchar(20);not null;index:idx_sync_history_status" json:"status"`       // success, failed
	Message    string     `gorm:"type:text" json:"message"`
	PaymentID  string     `gorm:"type:varchar(128);index:idx_sync_history_payment_id" json:"payment_id"` // PSP Payment ID
	ExecutedAt *time.Time `gorm:"not null;index:idx_sync_history_executed_at" json:"executed_at"`
	CreatedAt  *time.Time `gorm:"not null;index:idx_sync_history_created_at" json:"created_at"`
	UpdatedAt  *time.Time `gorm:"not null" json:"updated_at"`
}

// TableName 指定表名
func (SyncHistoryModel) TableName() string {
	return "sync_history"
}

// BeforeCreate GORM钩子，在创建前执行
func (s *SyncHistoryModel) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	s.CreatedAt = &now
	s.UpdatedAt = &now
	if s.ExecutedAt == nil {
		s.ExecutedAt = &now
	}
	return nil
}

// BeforeUpdate GORM钩子，在更新前执行
func (s *SyncHistoryModel) BeforeUpdate(tx *gorm.DB) error {
	now := time.Now()
	s.UpdatedAt = &now
	return nil
}

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&SyncHistoryModel{},
	)
}
