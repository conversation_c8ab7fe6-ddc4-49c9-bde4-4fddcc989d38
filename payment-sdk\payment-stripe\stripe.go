package paymentstripe

import (
	"encoding/json"
	"errors"
	"fmt"
	"payment-sdk/payment"

	"github.com/stripe/stripe-go/v82"
	"github.com/stripe/stripe-go/v82/charge"
	"github.com/stripe/stripe-go/v82/checkout/session"
	"github.com/stripe/stripe-go/v82/paymentintent"
	"github.com/stripe/stripe-go/v82/price"
	"github.com/stripe/stripe-go/v82/product"
	"github.com/stripe/stripe-go/v82/refund"
	"github.com/stripe/stripe-go/v82/webhook"
	"go.uber.org/zap"
)

const (
	PROVIDER_STRIPE = "stripe"
)

// PaymentGateway 支付网关接口
type PaymentGatewayStrip struct {
	eventMapper map[string]string
	Logger      *zap.Logger
	cfg         *payment.GatewayConfig
}

// NewPaymentGatewayStripe 创建支付网关
func NewPaymentGatewayStripe(gcfg *payment.GatewayConfig, logger *zap.Logger) *PaymentGatewayStrip {

	stripe.Key = gcfg.SecretKey

	g := &PaymentGatewayStrip{
		Logger: logger,
		eventMapper: map[string]string{
			"payment_intent.created":        payment.PaymentEventPending,
			"charge.succeeded":              payment.PaymentEventSucceeded,
			"payment_intent.canceled":       payment.PaymentEventCanceled,
			"payment_intent.payment_failed": payment.PaymentEventFailed,
			"checkout.session.expired":      payment.PaymentEventExpired,

			"charge.refunded": payment.RefundEventSucceeded,
			"refund.failed":   payment.RefundEventFailed,
		},
		cfg: gcfg,
	}

	return g
}

func (g *PaymentGatewayStrip) GetCbEvent(pspEvent string) string {
	cbEv, found := g.eventMapper[pspEvent]
	if !found {
		return payment.EventOthers
	}
	return cbEv
}

func (g *PaymentGatewayStrip) CreateOnetimeProductAndPrice(req *payment.CreateOnetimeProductAndPriceReq) (*payment.CreateOnetimeProductAndPriceRsp, error) {

	g.Logger.Debug("CreateOnetimeProductAndPrice",
		zap.Any("req", req),
	)

	// 创建 Product（产品）
	p, err := product.New(&stripe.ProductParams{
		Name:        stripe.String(req.ProductName),
		Description: stripe.String(req.ProductDescription),
	})
	if err != nil {
		return nil, fmt.Errorf("error while creating product %v", err.Error())
	}

	// 创建 Price（一次性付款价格）
	pr, err := price.New(&stripe.PriceParams{
		Product:    stripe.String(p.ID),
		Currency:   stripe.String(req.Currency),
		UnitAmount: stripe.Int64(req.UnitAmount), // $49.00
	})
	if err != nil {
		return nil, fmt.Errorf("error while creating price %v", err.Error())
	}

	rsp := &payment.CreateOnetimeProductAndPriceRsp{
		ProductID: p.ID,
		PriceID:   pr.ID,
	}
	return rsp, nil
}

// 修改产品名称、描述等
func (g *PaymentGatewayStrip) UpdateProductAndPrice(req *payment.UpdateOnetimeProductAndPriceReq) (*payment.UpdateOnetimeProductAndPriceRsp, error) {
	g.Logger.Debug("UpdateProductAndPrice",
		zap.Any("req", req),
	)

	rsp := &payment.UpdateOnetimeProductAndPriceRsp{}

	if len(req.ProductID) < 1 {
		return nil, fmt.Errorf("req.ProductID is empty")
	}
	if len(req.ProductName) > 0 && len(req.ProductDescription) > 0 {
		err := g.updateProduct(req)
		if err != nil {
			return nil, err
		}
		rsp.ProductID = req.ProductID
	}

	if len(req.PriceID) > 0 &&
		req.UnitAmount > 0 &&
		len(req.Currency) > 0 {
		newPriceID, err := g.updatePrice(req.ProductID,
			req.PriceID,
			req.UnitAmount,
			req.Currency,
			req.LookupKey)
		if err != nil {
			return nil, err
		}
		rsp.PriceIDRefeshed = true
		rsp.PriceID = newPriceID
	}
	return rsp, nil
}

func (g *PaymentGatewayStrip) updateProduct(req *payment.UpdateOnetimeProductAndPriceReq) error {
	g.Logger.Debug("updateProduct",
		zap.Any("req", req),
	)

	_, err := product.Update(req.ProductID, &stripe.ProductParams{
		Name:        stripe.String(req.ProductName),
		Description: stripe.String(req.ProductDescription),
	})
	if err != nil {
		return fmt.Errorf("error whileproduct.Update req.ProductID:%v, err: %v",
			req.ProductID, err.Error())
	}
	g.Logger.Debug("product.Update succ",
		zap.String("req.ProductName", req.ProductName),
		zap.String("req.ProductDescription", req.ProductDescription),
	)
	return nil
}

// 替换价格（新建价格 + 禁用旧价格）
func (g *PaymentGatewayStrip) updatePrice(productID, oldPriceID string, newAmount int64, newCurrency, newLookupKey string) (string, error) {
	g.Logger.Debug("updatePrice",
		zap.String("productID", productID),
		zap.String("oldPriceID", oldPriceID),
		zap.Int64("newAmount", newAmount),
		zap.String("newCurrency", newCurrency),
		zap.String("newLookupKey", newLookupKey),
	)

	// 创建新价格
	newPrice, err := price.New(&stripe.PriceParams{
		Product:    stripe.String(productID),
		Currency:   stripe.String(newCurrency),
		UnitAmount: stripe.Int64(newAmount),
		LookupKey:  stripe.String(newLookupKey),
	})
	if err != nil {
		return "", fmt.Errorf("error price.New productID:%v, err: %v",
			productID, err.Error())
	}
	g.Logger.Debug("price.New succ",
		zap.String("productID", productID),
		zap.String("newCurrency", newCurrency),
		zap.Int64("newAmount", newAmount),
		zap.String("newLookupKey", newLookupKey),
	)

	// 禁用旧价格
	_, err = price.Update(oldPriceID, &stripe.PriceParams{
		Active: stripe.Bool(false),
	})
	if err != nil {
		return "", fmt.Errorf("error price.Update productID:%v, oldPriceID:%v, err: %v", productID, oldPriceID, err.Error())
	}
	g.Logger.Debug("price.Update succ",
		zap.String("oldPriceID", oldPriceID),
		zap.Bool("Active", false),
	)

	return newPrice.ID, nil
}

func (g *PaymentGatewayStrip) ListPrices(productID string) ([]string, error) {
	g.Logger.Debug("ListPrices",
		zap.String("productID", productID),
	)

	// 创建查询参数
	params := &stripe.PriceListParams{}
	params.Limit = stripe.Int64(10) // 每页最多返回 10 个价格

	// 过滤指定产品的价格（如果你只想看某个产品的价格）
	params.Product = stripe.String(productID)

	// 调用 List
	iter := price.List(params)

	priceIDs := make([]string, 0)

	// 遍历结果
	for iter.Next() {
		p := iter.Price()
		priceIDs = append(priceIDs, p.ID)
		g.Logger.Debug("ListPrices",
			zap.String("Price ID", p.ID),
			zap.Bool("Price Active", p.Active),
			zap.Bool("Price Deleted", p.Deleted),
			zap.Any("Price Metadata", p.Metadata),
			zap.String("Product ID", p.Product.ID),
			zap.String("Currency", string(p.Currency)),
			zap.Int64("Unit Amount", p.UnitAmount),
			zap.Any("Recurring", p.Recurring),
		)
	}

	// 错误处理
	if err := iter.Err(); err != nil {
		g.Logger.Error("ListPrices", zap.Error(err))
		return nil, err
	}
	return priceIDs, nil
}

// 删除产品 + 禁用价格（逻辑删除）
func (g *PaymentGatewayStrip) DeleteProductAndDisablePrice(productID, priceID string) error {
	g.Logger.Debug("DeleteProductAndDisablePrice",
		zap.String("productID", productID),
		zap.String("priceID", priceID),
	)

	updatedPrice, err := price.Update(priceID, &stripe.PriceParams{
		Active: stripe.Bool(false),
	})
	if err != nil {
		g.Logger.Warn("PaymentGatewayStrip, failed to disable price",
			zap.Any("productID", productID),
			zap.Any("priceID", priceID),
			zap.Any("err.Error()", err.Error()),
		)
		return fmt.Errorf("failed to disable price, productID %v, priceID %v, err: %v",
			productID, priceID, err.Error())
	}
	g.Logger.Info("PaymentGatewayStrip",
		zap.Any("updatedPrice", updatedPrice),
	)

	g.ListPrices(productID)

	deletedProduct, err := product.Del(productID, nil)
	if err != nil {
		g.Logger.Warn("PaymentGatewayStrip, failed to delete product",
			zap.Any("productID", productID),
			zap.Any("priceID", priceID),
			zap.Any("err.Error()", err.Error()),
		)

		updateProductParams := &stripe.ProductParams{
			Active: stripe.Bool(false),
		}
		_, err1 := product.Update(productID, updateProductParams)
		if err1 != nil {
			g.Logger.Warn("PaymentGatewayStrip, failed to Update product",
				zap.Any("productID", productID),
				zap.Any("priceID", priceID),
				zap.Any("err1.Error()", err1.Error()),
			)
			return fmt.Errorf("failed to delete and update product, productID %v, priceID %v, err: %v",
				productID, priceID, err.Error())
		}
	}

	g.Logger.Info("PaymentGatewayStrip",
		zap.Any("deletedProduct", deletedProduct),
	)
	return nil
}

// CreateCheckout 创建结账会话
func (g *PaymentGatewayStrip) CreateCheckout(req *payment.CheckoutReq) (*payment.CreateCheckoutRsp, error) {

	g.Logger.Info("Creating checkout session",
		zap.String("order_id", req.OrderID),
		zap.String("price_id", req.PriceID),
		zap.String("trace_id", req.TraceID),
		zap.String("entitlement", req.Entitlement),
		zap.Any("req", req),
	)

	// Create new Checkout Session for the order
	// Other optional params include:
	// [billing_address_collection] - to display billing address details on the page
	// [customer] - if you have an existing Stripe Customer ID
	// [payment_intent_data] - lets capture the payment later
	// [customer_email] - lets you prefill the email input in the form
	// [automatic_tax] - to automatically calculate sales tax, VAT and GST in the checkout page
	// For full details see https://stripe.com/docs/api/checkout/sessions/create

	// ?session_id={CHECKOUT_SESSION_ID} means the redirect will have the session ID
	// set as a query param
	params := &stripe.CheckoutSessionParams{
		ClientReferenceID: stripe.String(req.OrderID),
		SuccessURL:        stripe.String(req.SuccessURL),
		CancelURL:         stripe.String(req.CancelURL),
		Mode:              stripe.String(string(stripe.CheckoutSessionModePayment)),
		LineItems: []*stripe.CheckoutSessionLineItemParams{
			{
				Quantity: stripe.Int64(int64(req.Quantity)),
				Price:    stripe.String(req.PriceID),
			},
		},
		// AutomaticTax: &stripe.CheckoutSessionAutomaticTaxParams{Enabled: stripe.Bool(true)},

		// 保存支付方式，以在会话外扣款
		// https://docs.stripe.com/payments/accept-a-payment?platform=web&ui=stripe-hosted#save-payment-methods-to-charge-them-off-session
		PaymentIntentData: &stripe.CheckoutSessionPaymentIntentDataParams{
			SetupFutureUsage: stripe.String("off_session"),
			Metadata: map[string]string{
				"flow":        "checkout",
				"order_id":    req.OrderID,
				"user_id":     req.UserID,
				"trace_id":    req.TraceID,
				"product_id":  req.ProductID,
				"country":     req.Country,
				"entitlement": req.Entitlement,
			},
		},
	}
	s, err := session.New(params)
	if err != nil {
		g.Logger.Warn("failed creating checkout session",
			zap.String("order_id", req.OrderID),
			zap.String("price_id", req.PriceID),
			zap.Any("req", req),
			zap.String("err", err.Error()),
		)
		return nil, fmt.Errorf("error while creating session %v", err.Error())
	}

	ret := &payment.CreateCheckoutRsp{
		CheckoutURL: s.URL,
		PaymentID:   s.ID,
	}

	return ret, nil
}

// GetPaymentIntentByPaymentIntentID 根据 Payment ID(也就是 CheckoutSession.ID ) 查询支付详情
func GetPaymentIntentByPaymentID(paymentID string) (*stripe.PaymentIntent, error) {
	s, err := session.Get(paymentID, nil)
	if err != nil {
		return nil, err
	}
	if s.PaymentIntent == nil {
		return nil, errors.New("session has no payment_intent")
	}

	pi, err := paymentintent.Get(s.PaymentIntent.ID, nil)
	if err != nil {
		return nil, err
	}
	return pi, nil
}

// GetPaymentIntentWithRefundsByPaymentID 根据 Payment ID(也就是 CheckoutSession.ID ) 查询支付详情
func GetPaymentIntentWithRefundsByPaymentID(paymentID string) (*stripe.PaymentIntent, *stripe.Charge, error) {
	// 获取 PaymentIntent
	s, err := session.Get(paymentID, nil)
	if err != nil {
		return nil, nil, err
	}
	if s.PaymentIntent == nil {
		return nil, nil, errors.New("session has no payment_intent")
	}

	pi, err := paymentintent.Get(s.PaymentIntent.ID, nil)
	if err != nil {
		return nil, nil, err
	}

	if pi.LatestCharge == nil || pi.LatestCharge.ID == "" {
		return pi, nil, nil // 没有关联的 Charge
	}

	// 获取最新的 Charge 详情
	ch, err := charge.Get(pi.LatestCharge.ID, nil)
	if err != nil {
		return pi, nil, err
	}

	return pi, ch, nil
}

// GetPaymentIntentByPaymentIntentID 根据 PaymentIntent ID 查询支付详情
func GetPaymentIntentByPaymentIntentID(paymentIntentID string) (*stripe.PaymentIntent, error) {
	if paymentIntentID == "" {
		return nil, errors.New("paymentIntentID 不能为空")
	}

	// 直接调用 Stripe API 获取 PaymentIntent
	pi, err := paymentintent.Get(paymentIntentID, nil)
	if err != nil {
		return nil, err
	}

	return pi, nil
}

// IsPaymentSucceeded 根据 Payment ID(也就是 CheckoutSession.ID ) 查询支付和退款状态
// 返回 付款成功, 退款成功, error
func IsPaymentSucceeded(paymentID string) (bool, bool, error) {

	paySucc := false
	refundSucc := false
	pi, refundCharge, err := GetPaymentIntentWithRefundsByPaymentID(paymentID)
	if err != nil {
		return paySucc, refundSucc, err
	} else {
		if pi.Status == "succeeded" {
			paySucc = true
		}
		if refundCharge != nil && refundCharge.Status == "succeeded" &&
			refundCharge.AmountRefunded == refundCharge.Amount {
			refundSucc = true
		}

	}
	return paySucc, refundSucc, nil
}

// GetPaymentIntentByPaymentIntentID 根据 PaymentIntent ID 查询支付详情
func (g *PaymentGatewayStrip) GetPaymentIntentByPaymentIntentID(paymentIntentID string) (*stripe.PaymentIntent, error) {
	if paymentIntentID == "" {
		return nil, errors.New("paymentIntentID 不能为空")
	}

	// 直接调用 Stripe API 获取 PaymentIntent
	pi, err := paymentintent.Get(paymentIntentID, nil)
	if err != nil {
		return nil, err
	}

	return pi, nil
}

// RefundPayment 退款
func (g *PaymentGatewayStrip) RefundPayment(orderID string, traceID string, pspPaymentID string, pspPaymentIntentID string, amount float64) (string, error) {
	g.Logger.Info("Processing refund",
		zap.String("order_id", orderID),
		zap.String("trace_id", traceID),
		zap.String("psp_payment_id", pspPaymentID),
		zap.String("psp_payment_intent_id", pspPaymentIntentID),
		zap.Float64("amount", amount),
	)

	if len(pspPaymentIntentID) < 1 {
		if len(pspPaymentID) < 1 {
			return "", fmt.Errorf("pspPaymentID and pspPaymentIntentID both empty")
		}
		session, err := session.Get(pspPaymentID, nil)
		if err != nil {
			fmt.Println("handleRefund err:", err)
			return "", fmt.Errorf("RefundPayment session.Get err:%v", err)
		}
		paymentIntent := session.PaymentIntent
		pspPaymentIntentID = paymentIntent.ID
	}

	params := &stripe.RefundParams{
		PaymentIntent: stripe.String(pspPaymentIntentID),
		Metadata: map[string]string{
			"flow":     "checkout",
			"order_id": orderID,
			"trace_id": traceID,
			"reason":   "user_cancelled",
		}}
	result, err := refund.New(params)
	if err != nil {
		g.Logger.Warn("failed refund.New",
			zap.String("order_id", orderID),
			zap.String("trace_id", traceID),
			zap.String("psp_payment_id", pspPaymentID),
			zap.String("psp_payment_intent_id", pspPaymentIntentID),
			zap.Float64("amount", amount),
			zap.String("err", err.Error()),
		)
		return "", err
	}

	return result.ID, nil
}

func (g *PaymentGatewayStrip) safeGetHeaderValue(header map[string][]string, key string) string {
	if header == nil {
		return ""
	}
	if header[key] == nil {
		return ""
	}
	if len(header[key]) == 0 {
		return ""
	}
	return header[key][0]
}

// Webhook 支付网关事件回调
func (g *PaymentGatewayStrip) Webhook(header map[string][]string,
	payload []byte,
	cb func(*payment.EventCallBackParams) error) error {
	g.Logger.Debug("Verifying Webhook",
		zap.String("header", g.safeGetHeaderValue(header, "Stripe-Signature")),
		zap.Int("payload_size", len(payload)),
	)

	event, err := webhook.ConstructEvent(payload,
		g.safeGetHeaderValue(header, "Stripe-Signature"),
		g.cfg.WebhookSecretKey)
	if err != nil {
		g.Logger.Warn("webhook.ConstructEvent", zap.Error(err))
		return err
	}

	g.Logger.Info("Strip Webhook >>>>",
		zap.String("event.Type", string(event.Type)),
		zap.Any("event", event),
	)

	var (
		orderId     string
		userId      string
		traceId     string
		msg         string // 用于支付失败/退款失败原因
		country     string
		entitlement string
		pspEmail    string
		pspName     string
		pspCountry  string
	)

	switch event.Type {
	case "charge.succeeded":
		var pi stripe.Charge
		if err := json.Unmarshal(event.Data.Raw, &pi); err != nil {
			return fmt.Errorf("failed to parse PaymentIntent: %w", err)
		}
		orderId = pi.Metadata["order_id"]
		userId = pi.Metadata["user_id"]
		traceId = pi.Metadata["trace_id"]
		country = pi.Metadata["country"]
		entitlement = pi.Metadata["entitlement"]

		if pi.BillingDetails != nil {
			pspEmail = pi.BillingDetails.Email
			pspName = pi.BillingDetails.Name
			if pi.BillingDetails.Address != nil {
				pspCountry = pi.BillingDetails.Address.Country
			}
		}

	case "payment_intent.payment_failed":
		var pi stripe.PaymentIntent
		if err := json.Unmarshal(event.Data.Raw, &pi); err != nil {
			return fmt.Errorf("failed to parse failed PaymentIntent: %w", err)
		}
		orderId = pi.Metadata["order_id"]
		userId = pi.Metadata["user_id"]
		traceId = pi.Metadata["trace_id"]
		country = pi.Metadata["country"]
		entitlement = pi.Metadata["entitlement"]

		if pi.PaymentMethod != nil && pi.PaymentMethod.BillingDetails != nil {
			pspEmail = pi.PaymentMethod.BillingDetails.Email
			pspName = pi.PaymentMethod.BillingDetails.Name
			pspCountry = pi.PaymentMethod.BillingDetails.Address.Country
		}

		if pi.LastPaymentError != nil {
			msg = fmt.Sprintf("code=%s, msg=%s",
				pi.LastPaymentError.Code,
				pi.LastPaymentError.Msg)
		}

	case "charge.refunded":
		var rf stripe.Refund
		if err := json.Unmarshal(event.Data.Raw, &rf); err != nil {
			return fmt.Errorf("failed to parse Refund: %w", err)
		}
		orderId = rf.Metadata["order_id"]
		userId = rf.Metadata["user_id"]
		traceId = rf.Metadata["trace_id"]
		country = rf.Metadata["country"]
		entitlement = rf.Metadata["entitlement"]

	case "refund.failed":
		var rf stripe.Refund
		if err := json.Unmarshal(event.Data.Raw, &rf); err != nil {
			return fmt.Errorf("failed to parse Refund: %w", err)
		}
		orderId = rf.Metadata["order_id"]
		userId = rf.Metadata["user_id"]
		traceId = rf.Metadata["trace_id"]
		country = rf.Metadata["country"]
		entitlement = rf.Metadata["entitlement"]

		if rf.FailureReason != "" {
			msg = fmt.Sprintf("failure_reason=%s", rf.FailureReason)
		}

	default:
		// 其他事件：尝试从通用字段提取元数据
		var obj map[string]interface{}
		if err := json.Unmarshal(event.Data.Raw, &obj); err == nil {
			if meta, ok := obj["metadata"].(map[string]interface{}); ok {
				if v, ok := meta["order_id"].(string); ok {
					orderId = v
				}
				if v, ok := meta["user_id"].(string); ok {
					userId = v
				}
				if v, ok := meta["trace_id"].(string); ok {
					traceId = v
				}
				if v, ok := meta["country"].(string); ok {
					country = v
				}
				if v, ok := meta["entitlement"].(string); ok {
					entitlement = v
				}
			}
		}
	}

	params := &payment.EventCallBackParams{
		Event:       g.GetCbEvent(string(event.Type)),
		OrderID:     orderId,
		UserID:      userId,
		TraceID:     traceId,
		Msg:         msg,
		Country:     country,
		Entitlement: entitlement,

		PSPEmail:   pspEmail,
		PSPName:    pspName,
		PSPCountry: pspCountry,
	}
	err = cb(params)
	if err != nil {
		g.Logger.Warn("Webhook failed to call cb", zap.Error(err))
		return err
	}
	return nil
}

func (g *PaymentGatewayStrip) WebhookDeprected(header map[string][]string, payload []byte, cb func(*payment.EventCallBackParams) error) error {

	// g.Logger.Debug("Verifying Webhook",
	// 	zap.String("header", g.safeGetHeaderValue(header, "Stripe-Signature")),
	// 	zap.Int("payload_size", len(payload)),
	// )

	// event, err := webhook.ConstructEvent(payload,
	// 	g.safeGetHeaderValue(header, "Stripe-Signature"),
	// 	stripe.Key)
	// if err != nil {
	// 	g.Logger.Warn("webhook.ConstructEvent", zap.Error(err))
	// 	return err
	// }

	// // 从 event.Data.Object 中获取 order_id 和 user_id
	// var pi stripe.PaymentIntent
	// if err := json.Unmarshal(event.Data.Raw, &pi); err != nil {
	// 	return fmt.Errorf("failed to parse PaymentIntent: %w", err)
	// }
	// orderId := pi.Metadata["order_id"]
	// userId := pi.Metadata["user_id"]
	// traceID := pi.Metadata["trace_id"]
	// msg := "" // 存放支付失败原因 或者退款失败原因, 回调给上层程序

	// params := &payment.EventCallBackParams{
	// 	Event:   g.GetCbEvent(event.Type),
	// 	OrderID: orderId,
	// 	UserID:  userId,
	// 	TraceID: traceID,
	// 	Msg:     msg,
	// }
	// err = cb(params)
	// if err != nil {
	// 	g.Logger.Warn("Webhook failed to call cb", zap.Error(err))
	// 	return err
	// }

	// switch event.Type {
	// // 订单和付款
	// case "payment_intent.created":
	// 	g.Logger.Info("event >>>> payment_intent.created")
	// case "payment_intent.succeeded":
	// 	g.Logger.Info("event >>>> payment_intent.succeeded")
	// case "checkout.session.completed":
	// 	g.Logger.Info("event >>>> checkout.session.completed")
	// case "checkout.session.expired":
	// 	g.Logger.Info("event >>>> checkout.session.expired")
	// case "payment_intent.canceled":
	// 	g.Logger.Info("event >>>> payment_intent.canceled")
	// case "payment_intent.payment_failed":
	// 	g.Logger.Info("event >>>> payment_intent.payment_failed")

	// // 产品和价格
	// case "product.created":
	// 	g.Logger.Info("event >>>> product.created")
	// case "plan.created":
	// 	g.Logger.Info("event >>>> plan.created")
	// case "price.created":
	// 	g.Logger.Info("event >>>> price.created")
	// case "product.updated":
	// 	g.Logger.Info("event >>>> product.updated")
	// case "plan.updated":
	// 	g.Logger.Info("event >>>> plan.updated")
	// case "price.updated":
	// 	g.Logger.Info("event >>>> price.updated")

	// // 以下暂时用不到
	// case "checkout.session.async_payment_succeeded":
	// 	g.Logger.Info("event >>>> checkout.session.async_payment_succeeded")

	// case "checkout.session.async_payment_failed":
	// 	g.Logger.Info("event >>>> checkout.session.async_payment_failed")

	// case "invoice.payment_succeeded":
	// 	// 每月成功续费，可以延长会员时间
	// case "invoice.payment_failed":
	// 	// 自动续费失败，提醒用户处理付款方式
	// }

	return nil
}

// IsPaymentSucceeded 根据 Payment ID(也就是 CheckoutSession.ID ) 查询支付和退款状态
// 返回 付款成功, 退款成功, error
func (g *PaymentGatewayStrip) IsPaymentSucceeded(paymentID string) (bool, bool, error) {
	g.Logger.Info("IsPaymentSucceeded", zap.String("paymentID", paymentID))

	// 获取 checkout session
	session, err := session.Get(paymentID, nil)
	if err != nil {
		g.Logger.Error("Failed to get checkout session", zap.String("paymentID", paymentID), zap.Error(err))
		return false, false, fmt.Errorf("failed to get checkout session: %w", err)
	}

	g.Logger.Info("Checkout session retrieved",
		zap.String("paymentID", paymentID),
		zap.String("payment_status", string(session.PaymentStatus)),
		zap.String("status", string(session.Status)))

	// 检查付款状态
	paymentSucceeded := session.PaymentStatus == stripe.CheckoutSessionPaymentStatusPaid

	// 检查退款状态
	refundSucceeded := false
	if session.PaymentIntent != nil {
		// 获取 payment intent 来检查退款状态
		pi, err := paymentintent.Get(session.PaymentIntent.ID, nil)
		if err != nil {
			g.Logger.Warn("Failed to get payment intent",
				zap.String("payment_intent_id", session.PaymentIntent.ID),
				zap.Error(err))
		} else {
			// 检查退款状态
			if pi.AmountReceived > 0 && pi.AmountReceived < pi.Amount {
				refundSucceeded = true
			}
		}
	}

	g.Logger.Info("Payment status checked",
		zap.String("paymentID", paymentID),
		zap.Bool("payment_succeeded", paymentSucceeded),
		zap.Bool("refund_succeeded", refundSucceeded))

	return paymentSucceeded, refundSucceeded, nil
}
