package main

import (
	"context"
	"fmt"
	"log"

	"payment-backend/internal/config"
	"payment-backend/internal/service"
	"payment-common/logger"
)

// 这是一个展示如何使用 PointService 的示例
func main() {
	// 1. 加载配置
	cfg, err := config.LoadConfig("../configs/config.dev.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 2. 创建日志记录器
	lg, err := logger.NewLogger(&cfg.Log)
	if err != nil {
		log.Fatalf("Failed to create logger: %v", err)
	}

	// 3. 创建 PointService
	pointService := service.NewPointService(cfg, lg)

	// 4. 使用示例
	ctx := context.Background()

	// 示例1: 成功充值积分
	fmt.Println("=== 示例1: 充值积分 ===")
	err = pointService.ChargePoints(ctx, 100.5, "订单支付成功返积分")
	if err != nil {
		fmt.Printf("充值失败: %v\n", err)
	} else {
		fmt.Println("充值成功!")
	}

	// 示例2: 参数验证错误
	fmt.Println("\n=== 示例2: 参数验证 ===")

	// 积分数量无效
	err = pointService.ChargePoints(ctx, 0, "测试")
	if err != nil {
		fmt.Printf("预期的验证错误: %v\n", err)
	}

	// 原因太长
	longReason := make([]byte, 100)
	for i := range longReason {
		longReason[i] = 'a'
	}
	err = pointService.ChargePoints(ctx, 50, string(longReason))
	if err != nil {
		fmt.Printf("预期的验证错误: %v\n", err)
	}

	// 示例3: 在业务逻辑中使用
	fmt.Println("\n=== 示例3: 业务逻辑集成 ===")
	simulateOrderPaymentSuccess(pointService, "ORDER-12345", 299.99)
}

// 模拟订单支付成功后的积分充值逻辑
func simulateOrderPaymentSuccess(pointService service.PointService, orderID string, amount float32) {
	ctx := context.Background()

	// 计算返积分（10%）
	points := amount * 0.1
	reason := fmt.Sprintf("订单 %s 支付成功返积分", orderID)

	fmt.Printf("订单 %s 支付成功，金额: %.2f，返积分: %.2f\n", orderID, amount, points)

	err := pointService.ChargePoints(ctx, points, reason)
	if err != nil {
		fmt.Printf("积分充值失败: %v\n", err)
		// 在实际业务中，这里可能需要记录日志或者进行重试
	} else {
		fmt.Printf("积分充值成功: %.2f 积分\n", points)
	}
}
