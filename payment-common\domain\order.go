package domain

import (
	"context"
	"time"

	"github.com/shopspring/decimal"
)

// Order 订单实体
type Order struct {
	ID                  uint64          `json:"id"`
	OrderID             string          `json:"order_id"`
	UserID              string          `json:"user_id"`
	TraceID             string          `json:"trace_id"`
	ProductID           string          `json:"product_id"`
	ProductDesc         string          `json:"product_desc"`
	ProductSnapshot     string          `json:"product_snapshot"`
	PriceID             string          `json:"price_id"`
	Quantity            uint32          `json:"quantity"`
	Amount              decimal.Decimal `json:"amount"`
	NetAmount           decimal.Decimal `json:"net_amount"`
	Currency            string          `json:"currency"`
	PayStatus           string          `json:"pay_status"`
	PayRet              string          `json:"pay_ret"`
	PayedMethod         string          `json:"payed_method"`
	PSPProvider         string          `json:"psp_provider"`
	CardNumber          string          `json:"card_number"`
	PayedAt             *time.Time      `json:"payed_at"`
	RefundStatus        string          `json:"refund_status"`
	RefundedAt          *time.Time      `json:"refunded_at"`
	PSPPaymentID        string          `json:"psp_payment_id"`
	PSPPaymentIntentID  string          `json:"psp_payment_intent_id"`
	PSPPaymentRefundID  string          `json:"psp_payment_refund_id"`
	PSPPaymentRefundRet string          `json:"psp_payment_refund_ret"`
	PSPCustomerID       string          `json:"psp_customer_id"`
	PSPCustomerEmail    string          `json:"psp_customer_email"`
	PSPCustomerName     string          `json:"psp_customer_name"`
	PSPCustomerCountry  string          `json:"psp_customer_country"`
	PSPSubscriptionID   string          `json:"psp_subscription_id"`
	CreatedAt           *time.Time      `json:"created_at"`
	UpdatedAt           *time.Time      `json:"updated_at"`
	Deleted             bool            `json:"deleted"`
	DeletedAt           *time.Time      `json:"deleted_at,omitempty"`
}

// 支付状态常量
const (
	PayStatusCreated   = "created"   // 已经创建
	PayStatusPaid      = "paid"      // 付款中
	PayStatusSucceeded = "succeeded" // 付款成功
	PayStatusFulfilled = "fulfilled" // 已履约
	PayStatusFailed    = "failed"    // 付款失败
	PayStatusExpired   = "expired"   // 付款超时
	PayStatusCancelled = "cancelled" // 付款已取消
)

// 退款状态常量
const (
	RefundStatusNone      = "none"      // 未申请退款
	RefundStatusRequested = "requested" // 已经发起退款申请
	RefundStatusSucceeded = "succeeded" // 退款成功
	RefundStatusFulfilled = "fulfilled" // 退款履约(扣积分)
	RefundStatusFailed    = "failed"    // 退款失败
)

// 产品快照。对应订单中的 ProductSnapshot 字段。 ProductSnapshot 存储的是 ProductSnapshot 结构 json 序列化之后再 base64 编码得到的字符串。
type ProductSnapshot struct {
	UserEmail   string `json:"user_email" comment:"用户邮箱"`
	Country     string `json:"country" comment:"订单国家"`
	PackageType string `json:"package_type" comment:"流量包类型"`
	Entitlement int64  `json:"entitlement" comment:"购买后获得的权益"`
}

// OrderRepository 订单仓储接口
type OrderRepository interface {
	GetByOrderID(orderID string) (*Order, error)
	GetByOrderIDForUpdate(tx any, orderID string) (*Order, error)
	UpdateWithTransaction(tx any, order *Order) error
	UpdateStatusSucceededWithTransaction(tx any, orderID string, payStatus string, refundRet string, newSnapshot string) error
	UpdateRefundSucceededWithTransaction(tx any, orderID string, refundStatus string, refundRet string) error
}

// PointService 积分服务接口
type PointService interface {
	WithUserContext(ctx context.Context, userID, country, traceID string) context.Context
	ChargePoints(ctx context.Context, points float32, reason string) error
}

// PointServiceHTTP 积分服务HTTP接口
type PointServiceHTTP interface {
	WithUserContext(ctx context.Context, userID, country, traceID string) context.Context
	ChargePoints(ctx context.Context, points float32, reason string) error
}

// Order 方法

// IsPaymentCompleted 检查支付是否完成
func (o *Order) IsPaymentCompleted() bool {
	return o.PayStatus == PayStatusSucceeded || o.PayStatus == PayStatusFulfilled
}

// IsRefundable 检查是否可以退款
func (o *Order) IsRefundable() bool {
	return o.IsPaymentCompleted() && o.RefundStatus == RefundStatusNone
}

// IsCancellable 检查是否可以取消
func (o *Order) IsCancellable() bool {
	return o.PayStatus == PayStatusCreated || o.PayStatus == PayStatusPaid
}

// MarkAsPaid 标记为已付款
func (o *Order) MarkAsPaid() {
	now := time.Now()
	o.PayStatus = PayStatusPaid
	o.PayedAt = &now
	o.UpdatedAt = &now
}

// MarkAsSucceeded 标记为付款成功
func (o *Order) MarkAsSucceeded() {
	now := time.Now()
	o.PayStatus = PayStatusSucceeded
	o.PayedAt = &now
	o.UpdatedAt = &now
}

// MarkAsFulfilled 标记为已履约
func (o *Order) MarkAsFulfilled() {
	now := time.Now()
	o.PayStatus = PayStatusFulfilled
	o.UpdatedAt = &now
}

// MarkAsFailed 标记为付款失败
func (o *Order) MarkAsFailed() {
	now := time.Now()
	o.PayStatus = PayStatusFailed
	o.UpdatedAt = &now
}

// MarkAsExpired 标记为已过期
func (o *Order) MarkAsExpired() {
	now := time.Now()
	o.PayStatus = PayStatusExpired
	o.UpdatedAt = &now
}

// MarkAsCancelled 标记为已取消
func (o *Order) MarkAsCancelled() {
	now := time.Now()
	o.PayStatus = PayStatusCancelled
	o.UpdatedAt = &now
}

// MarkAsRefundedRequested 标记为已申请退款
func (o *Order) MarkAsRefundedRequested(refundAmount decimal.Decimal) {
	now := time.Now()
	o.RefundStatus = RefundStatusRequested
	o.NetAmount = o.Amount.Sub(refundAmount)
	o.UpdatedAt = &now
}

// SoftDelete 软删除订单
func (o *Order) SoftDelete() {
	now := time.Now()
	o.Deleted = true
	o.DeletedAt = &now
	o.UpdatedAt = &now
}
