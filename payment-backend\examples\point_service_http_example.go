package main

import (
	"context"
	"fmt"
	"log"

	"payment-backend/internal/config"
	"payment-backend/internal/service"
	"payment-common/logger"
)

func main() {
	// 加载配置
	cfg, err := config.Load("")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 创建日志记录器
	lg, err := logger.New(&cfg.Log)
	if err != nil {
		log.Fatalf("Failed to create logger: %v", err)
	}

	// 创建 HTTP 方式的积分服务
	pointServiceHTTP := service.NewPointServiceHTTP(cfg, lg)

	// 示例1：基本使用
	fmt.Println("=== 示例1：基本使用 ===")
	ctx := context.Background()

	// 创建用户上下文
	ctx = pointServiceHTTP.WithUserContext(ctx, "user123", "US", "example-trace-id")

	// 充值积分
	err = pointServiceHTTP.ChargePoints(ctx, 100.5, "示例充值")
	if err != nil {
		fmt.Printf("充值失败: %v\n", err)
	} else {
		fmt.Println("充值成功!")
	}

	// 示例2：在业务逻辑中使用
	fmt.Println("\n=== 示例2：在业务逻辑中使用 ===")
	err = processOrderPayment(pointServiceHTTP, "user456", 299.99)
	if err != nil {
		fmt.Printf("处理订单支付失败: %v\n", err)
	} else {
		fmt.Println("订单支付处理成功!")
	}

	// 示例3：批量充值
	fmt.Println("\n=== 示例3：批量充值 ===")
	users := []struct {
		UserID string
		Points float32
		Reason string
	}{
		{"user001", 50.0, "新用户奖励"},
		{"user002", 25.0, "推荐奖励"},
		{"user003", 100.0, "活动奖励"},
	}

	for _, user := range users {
		ctx := pointServiceHTTP.WithUserContext(context.Background(), user.UserID, "US", "batch-charge")
		err := pointServiceHTTP.ChargePoints(ctx, user.Points, user.Reason)
		if err != nil {
			fmt.Printf("用户 %s 充值失败: %v\n", user.UserID, err)
		} else {
			fmt.Printf("用户 %s 充值成功: %.1f 积分\n", user.UserID, user.Points)
		}
	}
}

// processOrderPayment 处理订单支付的业务逻辑示例
func processOrderPayment(pointService service.PointServiceHTTP, userID string, orderAmount float32) error {
	ctx := context.Background()

	// 1. 计算奖励积分（1% 奖励）
	rewardPoints := orderAmount * 0.01

	// 2. 创建用户上下文
	ctx = pointService.WithUserContext(ctx, userID, "US", "order-payment")

	// 3. 充值奖励积分
	reason := fmt.Sprintf("订单支付奖励 - 订单金额: $%.2f", orderAmount)
	if err := pointService.ChargePoints(ctx, rewardPoints, reason); err != nil {
		return fmt.Errorf("充值奖励积分失败: %w", err)
	}

	fmt.Printf("用户 %s 获得 %.2f 积分奖励\n", userID, rewardPoints)
	return nil
}

// 注意：这个示例需要以下条件才能正常运行：
// 1. 正确配置 Nacos 配置中心
// 2. 用户服务正在运行并可通过 HTTP 访问
// 3. 配置文件中包含正确的 Nacos 连接信息
//
// 运行方式：
// go run examples/point_service_http_example.go
